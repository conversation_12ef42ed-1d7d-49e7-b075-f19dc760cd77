# Common Boundaries Feature Guide

## Overview

The enhanced boundary selection dialog now includes **Common Boundaries** functionality, allowing you to define the same top and bottom boundaries for all wells simultaneously. This significantly improves efficiency when working with multiple wells that share common geological markers.

## New Features

### 1. Common Boundaries Section
- Located at the top of the boundary selection dialog
- Provides controls for setting boundaries that apply to all wells
- Shows surface frequency information (e.g., "E8 (9/9 wells)")

### 2. Smart Surface Detection
- Automatically identifies surfaces that appear across multiple wells
- Prioritizes surfaces that exist in all wells
- Shows frequency information to help you make informed choices

### 3. One-Click Application
- "Apply to All Wells" button applies selected common boundaries instantly
- Provides immediate feedback on success/failure for each well
- Shows status indicators for each well

### 4. Enhanced Status Tracking
- **✓ Common**: Well is using common boundaries
- **Manual**: Well has individually selected boundaries  
- **⚠ Missing Surface**: Selected boundary doesn't exist in this well
- **⚠ Data Error**: Problem retrieving depth data

## How to Use

### Step 1: Enable Common Boundaries
1. Check the "Use common boundaries for all wells" checkbox
2. The common boundary dropdowns will become enabled

### Step 2: Select Common Boundaries
1. Choose a **Common Top Boundary** from the dropdown
   - Surfaces are shown with frequency info (e.g., "E8 (9/9 wells)")
   - Surfaces appearing in more wells are listed first
2. Choose a **Common Bottom Boundary** similarly

### Step 3: Apply to All Wells
1. Click the "Apply to All Wells" button
2. The system will:
   - Check if selected boundaries exist in each well
   - Apply boundaries where possible
   - Show status for each well
   - Display a summary of results

### Step 4: Review and Adjust
1. Check the status column for each well
2. Manually adjust individual wells if needed
3. Wells with missing surfaces can be handled individually

## Benefits

### Efficiency
- Set boundaries for all wells with just a few clicks
- No need to select boundaries individually for each well
- Significant time savings for large datasets

### Consistency
- Ensures all wells use the same geological markers
- Reduces human error in boundary selection
- Maintains standardized analysis parameters

### Flexibility
- Can still override individual wells if needed
- Clear status indicators show which wells need attention
- Graceful handling of missing boundaries

## Example Workflow

1. **Load your Excel file** with boundary data
2. **Open the boundary selection dialog**
3. **Review common surfaces** - look for surfaces that appear in most/all wells
4. **Enable common boundaries** by checking the checkbox
5. **Select top boundary** (e.g., "E8 (9/9 wells)")
6. **Select bottom boundary** (e.g., "F19 (8/9 wells)")
7. **Click "Apply to All Wells"**
8. **Review status** - most wells should show "✓ Common"
9. **Handle exceptions** - manually set boundaries for wells with "⚠" status
10. **Click OK** to complete the selection

## Technical Details

### Surface Frequency Analysis
The system analyzes your Excel data to identify:
- Which surfaces appear in which wells
- How many wells contain each surface
- The most commonly available surfaces across your dataset

### Error Handling
- **Missing Surface**: If a selected common boundary doesn't exist in a well, it's marked with "⚠ Missing Surface"
- **Data Error**: If there's a problem retrieving depth data, it's marked with "⚠ Data Error"
- **Partial Success**: The system continues processing other wells even if some fail

### Backward Compatibility
- All existing functionality remains unchanged
- Individual well selection still works as before
- The new common boundaries feature is optional

## Troubleshooting

### Common Issues

**Q: The common boundary dropdowns are empty**
A: This means no surfaces appear in multiple wells. Check your Excel data format and ensure well names match between Excel and LAS files.

**Q: Some wells show "Missing Surface" status**
A: The selected common boundary doesn't exist in those wells. You can either:
- Choose a different common boundary that exists in more wells
- Manually set boundaries for the affected wells

**Q: The "Apply to All Wells" button is disabled**
A: Make sure you've checked the "Use common boundaries for all wells" checkbox first.

### Data Requirements

Your Excel file should contain:
- **Well** column: Well names matching your LAS files
- **Surface** column: Geological marker names
- **MD** column: Measured depth values

### Best Practices

1. **Choose surfaces wisely**: Select boundaries that exist in most of your wells
2. **Review before applying**: Check the frequency information in parentheses
3. **Handle exceptions**: Don't ignore wells with warning status
4. **Validate results**: Review the final summary before proceeding

## Code Implementation

The new functionality is implemented in `ui/dialog_systems.py` with these key components:

### New Methods
- `_find_common_surfaces()`: Analyzes surface frequency across wells
- Enhanced `_select_boundaries_for_all_wells()`: Includes common boundary UI

### Key Features
- Dynamic surface frequency calculation
- Real-time status updates
- Comprehensive error handling
- Enhanced user feedback

This feature maintains full backward compatibility while significantly improving the user experience for multi-well boundary selection.