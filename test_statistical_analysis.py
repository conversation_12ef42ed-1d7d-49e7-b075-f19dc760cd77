#!/usr/bin/env python3
"""
Test Script for Statistical Analysis Module

This script demonstrates the statistical analysis functionality for EEI/CPEI/PEIL
multi-well analysis results. It creates sample data and shows how the statistical
analysis module generates histograms and statistical summaries.

Usage:
    python test_statistical_analysis.py

Author: Assistant
Date: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_sample_well_data(num_wells=5, num_points=100):
    """
    Create sample well data for testing statistical analysis.
    
    Args:
        num_wells: Number of wells to simulate
        num_points: Number of data points per well
        
    Returns:
        Tuple of (all_wells_data, all_wells_results)
    """
    all_wells_data = []
    all_wells_results = []
    
    # Base parameters for realistic variation
    base_eei_mean = 0.5
    base_target_mean = 0.3
    base_angle = 25.0
    
    for i in range(num_wells):
        well_name = f"WELL_{i+1:02d}"
        
        # Add some realistic variation between wells
        eei_offset = np.random.normal(0, 0.1)
        target_offset = np.random.normal(0, 0.05)
        angle_offset = np.random.normal(0, 5.0)
        
        # Generate synthetic data with some correlation
        depth = np.linspace(1000 + i*100, 1200 + i*100, num_points)
        
        # Create correlated EEI and target data
        noise = np.random.normal(0, 0.05, num_points)
        trend = np.linspace(-0.1, 0.1, num_points)
        
        normalized_eei = base_eei_mean + eei_offset + trend + noise
        target = base_target_mean + target_offset + 0.7 * normalized_eei + np.random.normal(0, 0.03, num_points)
        
        # Add some realistic parameter variations
        angle = base_angle + angle_offset
        correlation = np.corrcoef(normalized_eei, target)[0, 1]
        
        # Create well data dictionary
        well_data = {
            'well_name': well_name,
            'depth': depth,
            'normalized_eei': normalized_eei,
            'target': target,
            'angle': f"{angle:.1f}°",
            'correlation': correlation
        }
        all_wells_data.append(well_data)
        
        # Create results dictionary
        if i % 3 == 0:  # EEI results
            result = {
                'well_name': well_name,
                'analysis_type': 'EEI',
                'optimum_angle': angle,
                'max_correlation': correlation,
                'parameters': f"Angle: {angle:.1f}°"
            }
        elif i % 3 == 1:  # CPEI results
            n_val = np.random.uniform(1.5, 2.5)
            phi_val = np.random.uniform(0.1, 0.4)
            result = {
                'well_name': well_name,
                'analysis_type': 'CPEI',
                'optimal_n': n_val,
                'optimal_phi': phi_val,
                'max_correlation': correlation,
                'parameters': f"n={n_val:.2f}, φ={phi_val:.2f}"
            }
        else:  # PEIL results
            n_val = np.random.uniform(1.0, 3.0)
            phi_val = np.random.uniform(0.05, 0.3)
            result = {
                'well_name': well_name,
                'analysis_type': 'PEIL',
                'optimal_n': n_val,
                'optimal_phi': phi_val,
                'max_correlation': correlation,
                'parameters': f"n={n_val:.2f}, φ={phi_val:.2f}"
            }
        
        all_wells_results.append(result)
    
    return all_wells_data, all_wells_results


def test_eei_statistical_analysis():
    """Test statistical analysis for EEI data."""
    print("Testing EEI Statistical Analysis...")
    
    # Create sample EEI data
    all_wells_data, all_wells_results = create_sample_well_data(num_wells=6)
    
    # Make all results EEI type for this test
    for i, result in enumerate(all_wells_results):
        angle = 20 + np.random.normal(0, 5)
        result.update({
            'analysis_type': 'EEI',
            'optimum_angle': angle,
            'max_correlation': 0.7 + np.random.normal(0, 0.1),
            'parameters': f"Angle: {angle:.1f}°"
        })
    
    try:
        from ui.statistical_analysis import get_statistical_analyzer
        
        analyzer = get_statistical_analyzer()
        
        # Test distribution analysis
        print("  - Analyzing value distributions...")
        dist_analysis = analyzer.analyze_well_data_distributions(all_wells_data, 'EEI')
        
        # Test parameter analysis
        print("  - Analyzing optimization parameters...")
        param_analysis = analyzer.analyze_optimization_parameters(all_wells_results, 'EEI')
        
        # Test plotting functions
        print("  - Generating distribution plots...")
        analyzer.plot_value_distributions(dist_analysis)
        
        print("  - Generating parameter plots...")
        analyzer.plot_parameter_distributions(param_analysis)
        
        print("  - Generating box plots...")
        analyzer.plot_well_comparison_boxplots(all_wells_data, 'EEI')
        
        # Test statistical report
        print("  - Generating statistical report...")
        report = analyzer.generate_statistical_report(dist_analysis, param_analysis)
        print("\nStatistical Report:")
        print("=" * 50)
        print(report)
        
        print("✓ EEI Statistical Analysis completed successfully!")
        
    except Exception as e:
        print(f"✗ Error in EEI statistical analysis: {str(e)}")
        import traceback
        traceback.print_exc()


def test_cpei_statistical_analysis():
    """Test statistical analysis for CPEI data."""
    print("\nTesting CPEI Statistical Analysis...")
    
    # Create sample CPEI data
    all_wells_data, all_wells_results = create_sample_well_data(num_wells=5)
    
    # Make all results CPEI type for this test
    for i, result in enumerate(all_wells_results):
        n_val = 1.5 + np.random.normal(0, 0.3)
        phi_val = 0.2 + np.random.normal(0, 0.05)
        result.update({
            'analysis_type': 'CPEI',
            'optimal_n': n_val,
            'optimal_phi': phi_val,
            'max_correlation': 0.6 + np.random.normal(0, 0.15),
            'parameters': f"n={n_val:.2f}, φ={phi_val:.2f}"
        })
    
    try:
        from ui.statistical_analysis import get_statistical_analyzer
        
        analyzer = get_statistical_analyzer()
        
        # Test distribution analysis
        print("  - Analyzing value distributions...")
        dist_analysis = analyzer.analyze_well_data_distributions(all_wells_data, 'CPEI')
        
        # Test parameter analysis
        print("  - Analyzing optimization parameters...")
        param_analysis = analyzer.analyze_optimization_parameters(all_wells_results, 'CPEI')
        
        # Test plotting functions
        print("  - Generating plots...")
        analyzer.plot_value_distributions(dist_analysis)
        analyzer.plot_parameter_distributions(param_analysis)
        
        # Test statistical report
        print("  - Generating statistical report...")
        report = analyzer.generate_statistical_report(dist_analysis, param_analysis)
        print("\nCPEI Statistical Report:")
        print("=" * 50)
        print(report)
        
        print("✓ CPEI Statistical Analysis completed successfully!")
        
    except Exception as e:
        print(f"✗ Error in CPEI statistical analysis: {str(e)}")


def test_integration_with_plotting_components():
    """Test integration with plotting components."""
    print("\nTesting Integration with Plotting Components...")
    
    try:
        from ui.plotting_components import (
            plot_statistical_analysis, plot_distribution_histograms,
            plot_parameter_histograms, plot_well_comparison_boxplots
        )
        
        # Create sample data
        all_wells_data, all_wells_results = create_sample_well_data(num_wells=4)
        
        print("  - Testing comprehensive statistical analysis...")
        plot_statistical_analysis(all_wells_data, all_wells_results, 'EEI')
        
        print("  - Testing individual plotting functions...")
        plot_distribution_histograms(all_wells_data, 'EEI')
        plot_parameter_histograms(all_wells_results, 'EEI')
        plot_well_comparison_boxplots(all_wells_data, 'EEI')
        
        print("✓ Integration testing completed successfully!")
        
    except Exception as e:
        print(f"✗ Error in integration testing: {str(e)}")


def main():
    """Main test function."""
    print("Statistical Analysis Module Test Suite")
    print("=" * 50)
    
    # Set matplotlib to non-interactive mode for testing
    plt.ioff()
    
    try:
        # Test individual components
        test_eei_statistical_analysis()
        test_cpei_statistical_analysis()
        test_integration_with_plotting_components()
        
        print("\n" + "=" * 50)
        print("All tests completed!")
        print("Note: Plots have been generated but not displayed.")
        print("In a real application, plt.show() would display the plots.")
        
    except ImportError as e:
        print(f"Import error: {str(e)}")
        print("Make sure all required modules are available.")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()