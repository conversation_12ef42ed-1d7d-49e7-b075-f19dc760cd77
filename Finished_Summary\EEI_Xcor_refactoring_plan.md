# EEI Cross-Correlation Analysis Refactoring Plan
## Comprehensive Step-by-Step Guideline for Modular Architecture Transformation

---

## Executive Summary

This document provides a comprehensive, step-by-step refactoring guideline to transform the monolithic [`a7_load_multilas_EEI_XCOR_PLOT_Final.py`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1) script (4,942 lines) into a modular, maintainable architecture while preserving 100% of existing functionality. The refactoring follows a **UI-Preserving Backend Modularization** strategy, targeting a 20-25% code reduction through intelligent extraction of pure calculation logic and utilities.

### Key Principles
- **🛡️ UI Preservation First**: All sophisticated user interface components remain intact
- **🔧 Backend Modularization Only**: Extract pure calculation logic and utilities
- **📊 Functionality Preservation**: Maintain 100% backward compatibility
- **🎯 Clean Architecture**: Establish clear separation between UI and business logic

---

## Current Architecture Analysis

### File Structure Overview
**Current Monolithic File**: [`a7_load_multilas_EEI_XCOR_PLOT_Final.py`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1) (4,942 lines)

| Section | Lines | Type | Refactoring Strategy |
|---------|-------|------|---------------------|
| Helper functions | 30-163 | Utilities | **PRESERVE** - Essential for UI error handling |
| **Calculator UI** | 165-762 | **UI Excellence** | **PRESERVE FULLY** - Sophisticated interface |
| Log analysis functions | 764-1179 | Mixed | **PARTIAL** - Keep UI, extract pure logic |
| Analysis optimization | 1180-1432 | Backend | **EXTRACT** - Pure calculation logic |
| **CPEI/PEIL functions** | 1433-1911 | **Backend Duplication** | **EXTRACT** - Major consolidation target |
| Plotting functions | 1913-2427 | UI Integration | **PRESERVE** - Tightly coupled to UI |
| Individual/merged analysis | 2429-3307 | Workflow | **PRESERVE** - Core orchestration |
| **Dialog functions** | 3309-4539 | **UI Excellence** | **PRESERVE FULLY** - Professional dialogs |
| Main execution | 4541-4942 | Workflow | **PRESERVE** - User experience flow |

### UI Excellence Components (Must Preserve)
1. **🎯 Sophisticated Calculator Interface** (lines 532-707)
2. **🎯 Professional Dialog System** (lines 3309-4539)
3. **🎯 Integrated Workflow Experience** (main execution flow)

---

## Target Architecture

### New Modular Structure
```
📁 EEI Analysis Project
├── 📄 a7_load_multilas_EEI_XCOR_PLOT_Final.py (~3,700-3,900 lines) ← MAIN UI FILE
│   ├── 🎯 ALL Calculator UI components (PRESERVED)
│   ├── 🎯 ALL Dialog systems (PRESERVED)
│   ├── 🎯 ALL Workflow orchestration (PRESERVED)
│   ├── 🎯 ALL Plotting with UI integration (PRESERVED)
│   └── 🎯 ALL User interaction logic (PRESERVED)
├── 📄 eei_calculation_engine.py (~400 lines) ← BACKEND CALCULATION ENGINE
├── 📄 eei_data_processing.py (~300 lines) ← DATA PROCESSING UTILITIES
├── 📄 eei_config.py (~100 lines) ← CONFIGURATION MANAGEMENT
└── 📄 eeimpcalc.py (existing, enhanced) ← CORE EEI CALCULATIONS
```

---

## Phase 1: Backend Calculation Engine Extraction

### 1.1 Create eei_calculation_engine.py

**Purpose**: Extract pure calculation logic while preserving ALL UI components

#### Functions to Extract (Backend Only)
- [`calculate_cpei_optimum_parameters()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1433) (lines 1433-1537)
- [`calculate_peil_optimum_parameters()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1539) (lines 1539-1643)
- [`calculate_eei_optimum_angle()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1252) (lines 1252-1353)
- [`calculate_eei_optimum_angle_merged()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1355) (lines 1355-1431)

#### Implementation Strategy

**Step 1.1.1: Create Base Calculation Engine Class**

```python
# eei_calculation_engine.py
"""
EEI Calculation Engine Module

This module contains pure calculation logic for EEI, CPEI, and PEIL optimization.
All functions are stateless and have no UI dependencies.

Author: [Your Name]
Created: [Date]
Version: 1.0.0
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union
from eeimpcalc import eeimpcalc, calculate_cpei, calculate_peil

# Configure module-specific logger
logger = logging.getLogger(__name__)

class EEICalculationEngine:
    """
    Pure calculation engine for EEI, CPEI, and PEIL optimization.
    
    This class contains only stateless calculation methods with no UI dependencies.
    All methods are static to emphasize the stateless nature.
    """
    
    @staticmethod
    def calculate_cpei_optimization(
        pvel: np.ndarray,
        svel: np.ndarray, 
        rhob: np.ndarray,
        target: np.ndarray,
        n_range: Optional[np.ndarray] = None,
        phi_range: Optional[range] = None
    ) -> Dict[str, Union[np.ndarray, float, int]]:
        """
        Pure CPEI optimization calculation.
        
        Args:
            pvel: P-wave velocity array
            svel: S-wave velocity array  
            rhob: Density array
            target: Target log array
            n_range: Range of n values to test (default: 0.1 to 2.0, step 0.1)
            phi_range: Range of phi values to test (default: -90 to 90 degrees)
            
        Returns:
            Dict containing:
                - correlation_matrix: 2D correlation matrix
                - n_values: Array of n values tested
                - phi_values: Array of phi values tested
                - optimal_params: Dict with optimal n, phi, and max correlation
                
        Raises:
            ValueError: If input arrays are invalid or incompatible
            RuntimeError: If optimization fails
        """
        try:
            # Input validation
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "CPEI"
            )
            
            # Set default ranges
            n_values = n_range if n_range is not None else np.arange(0.1, 2.1, 0.1)
            phi_values = phi_range if phi_range is not None else range(-90, 91)
            
            # Initialize correlation matrix
            correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)
            
            logger.info(f"Starting CPEI optimization: {len(n_values)} n values × {len(phi_values)} phi values")
            
            # Optimization loop
            for i, n in enumerate(n_values):
                for j, phi in enumerate(phi_values):
                    try:
                        cpei = calculate_cpei(pvel, svel, rhob, n, phi)
                        correlation = EEICalculationEngine._calculate_correlation_safe(cpei, target)
                        correlation_matrix[i, j] = correlation
                    except Exception as e:
                        logger.warning(f"CPEI calculation failed for n={n:.1f}, phi={phi}: {str(e)}")
                        correlation_matrix[i, j] = np.nan
            
            # Find optimal parameters
            optimal_params = EEICalculationEngine._find_optimal_2d(
                correlation_matrix, n_values, phi_values
            )
            
            logger.info(f"CPEI optimization complete. Optimal: n={optimal_params['n']:.1f}, phi={optimal_params['phi']}°")
            
            return {
                'correlation_matrix': correlation_matrix,
                'n_values': n_values,
                'phi_values': phi_values,
                'optimal_params': optimal_params
            }
            
        except Exception as e:
            logger.error(f"CPEI optimization failed: {str(e)}")
            raise RuntimeError(f"CPEI optimization failed: {str(e)}") from e
    
    @staticmethod
    def calculate_peil_optimization(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray, 
        target: np.ndarray,
        n_range: Optional[np.ndarray] = None,
        phi_range: Optional[range] = None
    ) -> Dict[str, Union[np.ndarray, float, int]]:
        """
        Pure PEIL optimization calculation.
        
        Identical structure to CPEI optimization but uses calculate_peil().
        
        Args:
            pvel: P-wave velocity array
            svel: S-wave velocity array
            rhob: Density array  
            target: Target log array
            n_range: Range of n values to test
            phi_range: Range of phi values to test
            
        Returns:
            Dict containing optimization results
            
        Raises:
            ValueError: If input arrays are invalid
            RuntimeError: If optimization fails
        """
        try:
            # Input validation
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "PEIL"
            )
            
            # Set default ranges
            n_values = n_range if n_range is not None else np.arange(0.1, 2.1, 0.1)
            phi_values = phi_range if phi_range is not None else range(-90, 91)
            
            # Initialize correlation matrix
            correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)
            
            logger.info(f"Starting PEIL optimization: {len(n_values)} n values × {len(phi_values)} phi values")
            
            # Optimization loop
            for i, n in enumerate(n_values):
                for j, phi in enumerate(phi_values):
                    try:
                        peil = calculate_peil(pvel, svel, rhob, n, phi)
                        correlation = EEICalculationEngine._calculate_correlation_safe(peil, target)
                        correlation_matrix[i, j] = correlation
                    except Exception as e:
                        logger.warning(f"PEIL calculation failed for n={n:.1f}, phi={phi}: {str(e)}")
                        correlation_matrix[i, j] = np.nan
            
            # Find optimal parameters
            optimal_params = EEICalculationEngine._find_optimal_2d(
                correlation_matrix, n_values, phi_values
            )
            
            logger.info(f"PEIL optimization complete. Optimal: n={optimal_params['n']:.1f}, phi={optimal_params['phi']}°")
            
            return {
                'correlation_matrix': correlation_matrix,
                'n_values': n_values,
                'phi_values': phi_values,
                'optimal_params': optimal_params
            }
            
        except Exception as e:
            logger.error(f"PEIL optimization failed: {str(e)}")
            raise RuntimeError(f"PEIL optimization failed: {str(e)}") from e
    
    @staticmethod
    def calculate_eei_optimization(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray,
        target: np.ndarray,
        calcmethod: int = 3,
        k_method: int = 1,
        k_value: Optional[float] = None,
        angle_range: Optional[range] = None
    ) -> Dict[str, Union[np.ndarray, float, int]]:
        """
        Pure EEI angle optimization calculation.
        
        Args:
            pvel: P-wave velocity array
            svel: S-wave velocity array
            rhob: Density array
            target: Target log array
            calcmethod: EEI calculation method (1, 2, or 3)
            k_method: K value determination method (1=calculate, 2=constant)
            k_value: Constant k value (if k_method=2)
            angle_range: Range of angles to test (default: -90 to 90 degrees)
            
        Returns:
            Dict containing:
                - correlations: Array of correlation values
                - angles: Array of angles tested
                - optimal_angle: Optimal angle in degrees
                - max_correlation: Maximum correlation achieved
                - k_used: K value used in calculations
                
        Raises:
            ValueError: If input parameters are invalid
            RuntimeError: If optimization fails
        """
        try:
            # Input validation
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "EEI"
            )
            
            # Determine k value
            k = EEICalculationEngine._determine_k_value(
                pvel, svel, k_method, k_value
            )
            
            # Set default angle range
            angles = angle_range if angle_range is not None else range(-90, 91)
            correlations = []
            
            logger.info(f"Starting EEI optimization: {len(angles)} angles, k={k:.4f}")
            
            # Optimization loop
            for angle in angles:
                try:
                    eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)
                    correlation = EEICalculationEngine._calculate_correlation_safe(eei, target)
                    correlations.append(correlation)
                except Exception as e:
                    logger.warning(f"EEI calculation failed for angle {angle}: {str(e)}")
                    correlations.append(np.nan)
            
            # Find optimal angle
            correlations = np.array(correlations)
            optimal_result = EEICalculationEngine._find_optimal_1d(correlations, angles)
            
            logger.info(f"EEI optimization complete. Optimal angle: {optimal_result['optimal_value']}°")
            
            return {
                'correlations': correlations,
                'angles': list(angles),
                'optimal_angle': optimal_result['optimal_value'],
                'max_correlation': optimal_result['max_correlation'],
                'k_used': k
            }
            
        except Exception as e:
            logger.error(f"EEI optimization failed: {str(e)}")
            raise RuntimeError(f"EEI optimization failed: {str(e)}") from e
    
    # Private helper methods
    @staticmethod
    def _validate_optimization_inputs(
        pvel: np.ndarray,
        svel: np.ndarray, 
        rhob: np.ndarray,
        target: np.ndarray,
        analysis_type: str
    ) -> None:
        """Validate inputs for optimization calculations."""
        if not all(isinstance(arr, np.ndarray) for arr in [pvel, svel, rhob, target]):
            raise ValueError("All input arrays must be numpy arrays")
        
        if not all(arr.size > 0 for arr in [pvel, svel, rhob, target]):
            raise ValueError("All input arrays must be non-empty")
        
        if not all(arr.shape == pvel.shape for arr in [svel, rhob, target]):
            raise ValueError("All input arrays must have the same shape")
        
        if not all(np.isfinite(arr).any() for arr in [pvel, svel, rhob, target]):
            raise ValueError(f"All input arrays must contain at least some finite values for {analysis_type}")
    
    @staticmethod
    def _calculate_correlation_safe(x: np.ndarray, y: np.ndarray) -> float:
        """Calculate correlation coefficient with comprehensive error handling."""
        try:
            # Remove NaN values
            mask = np.isfinite(x) & np.isfinite(y)
            if np.sum(mask) < 2:
                return np.nan
            
            x_clean = x[mask]
            y_clean = y[mask]
            
            # Check for constant arrays
            if np.all(x_clean == x_clean[0]) or np.all(y_clean == y_clean[0]):
                return np.nan
            
            return np.corrcoef(x_clean, y_clean)[0, 1]
        
        except Exception:
            return np.nan
    
    @staticmethod
    def _find_optimal_2d(
        correlation_matrix: np.ndarray,
        n_values: np.ndarray,
        phi_values: range
    ) -> Dict[str, float]:
        """Find optimal parameters from 2D correlation matrix."""
        if np.all(np.isnan(correlation_matrix)):
            return {'n': np.nan, 'phi': np.nan, 'max_correlation': np.nan}
        
        max_idx = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)
        
        return {
            'n': n_values[max_idx[0]],
            'phi': phi_values[max_idx[1]], 
            'max_correlation': correlation_matrix[max_idx]
        }
    
    @staticmethod
    def _find_optimal_1d(
        correlations: np.ndarray,
        values: range
    ) -> Dict[str, float]:
        """Find optimal value from 1D correlation array."""
        if np.all(np.isnan(correlations)):
            return {'optimal_value': np.nan, 'max_correlation': np.nan}
        
        max_idx = np.nanargmax(correlations)
        
        return {
            'optimal_value': values[max_idx],
            'max_correlation': correlations[max_idx]
        }
    
    @staticmethod
    def _determine_k_value(
        pvel: np.ndarray,
        svel: np.ndarray,
        k_method: int,
        k_value: Optional[float]
    ) -> float:
        """Determine k value based on method."""
        if k_method == 1:
            # Calculate from velocity ratio
            velocity_ratio = svel / pvel
            k = np.nanmean(velocity_ratio**2)
            
            if not np.isfinite(k) or k <= 0:
                logger.warning(f"Invalid calculated k value: {k}, using default 0.25")
                k = 0.25
                
            return k
        else:
            # Use provided constant value
            if k_value is None or not np.isfinite(k_value) or k_value <= 0:
                logger.warning(f"Invalid provided k value: {k_value}, using default 0.25")
                return 0.25
            
            return k_value
```

**Step 1.1.2: Update Main File Integration**

In the main file, replace the original functions with UI wrapper functions:

```python
# In a7_load_multilas_EEI_XCOR_PLOT_Final.py
from eei_calculation_engine import EEICalculationEngine

def calculate_cpei_optimum_parameters(las, actual_base_log_mnemonics, target_log_actual_mnemonic,
                                    top_depth, bottom_depth):
    """
    UI wrapper for CPEI optimization - preserves all existing UI functionality.
    
    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """
    
    # ALL existing UI code remains identical:
    # - Progress dialogs
    # - Error handling  
    # - User feedback
    # - Data extraction and validation
    
    print(f"Starting CPEI optimization for target log: {target_log_actual_mnemonic}")
    
    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)
    
    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)
    
    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index
    
    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]
    
    # Convert to velocities (existing code preserved)
    pvel = 304800 / dt
    svel = 304800 / dts
    
    try:
        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_cpei_optimization(
            pvel, svel, rhob, target
        )
        
        # Extract results for UI display (preserving existing format)
        optimal_n = results['optimal_params']['n']
        optimal_phi = results['optimal_params']['phi']
        max_correlation = results['optimal_params']['max_correlation']
        n_values = results['n_values']
        phi_values = results['phi_values']
        correlation_matrix = results['correlation_matrix']
        
        # ALL existing result processing and UI updates remain identical
        print(f"CPEI optimization complete:")
        print(f"  Optimal n: {safe_format_float(optimal_n, precision=1, default='N/A')}")
        print(f"  Optimal phi: {optimal_phi}°")
        print(f"  Maximum correlation: {safe_format_float(max_correlation, precision=4, default='N/A')}")
        
        return optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix
        
    except Exception as e:
        # Enhanced error handling with backend error context
        logger.error(f"CPEI optimization failed: {str(e)}")
        print(f"Error in CPEI optimization: {str(e)}")
        return None, None, None, np.arange(0.1, 2.1, 0.1), range(-90, 91), np.full((20, 181), np.nan)
```

---

## Phase 2: Data Processing Utilities Extraction

### 2.1 Create eei_data_processing.py

**Purpose**: Extract pure data processing utilities while preserving ALL UI integration

#### Functions to Extract
- [`nanaware_corrcoef()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1036) (lines 1036-1078)
- [`find_nearest_index()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1080) (lines 1080-1082)
- [`merge_well_data()`](a7_load_multilas_EEI_XCOR_PLOT_Final.py:1084) (lines 1084-1178)

#### Implementation Strategy

```python
# eei_data_processing.py
"""
EEI Data Processing Utilities Module

This module contains pure data processing utilities with no UI dependencies.
All functions are stateless and focused on data manipulation and analysis.

Author: [Your Name]
Created: [Date]
Version: 1.0.0
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union, Any

# Configure module-specific logger
logger = logging.getLogger(__name__)

class EEIDataProcessor:
    """
    Pure data processing utilities for EEI analysis.
    
    This class contains only stateless data processing methods with no UI dependencies.
    All methods are static to emphasize the stateless nature.
    """
    
    @staticmethod
    def calculate_correlation_safe(x: np.ndarray, y: np.ndarray) -> float:
        """
        Calculate correlation coefficient with comprehensive error handling.
        
        This function mimics MATLAB's corr function with 'rows' set to 'complete'.
        
        Args:
            x: First data array
            y: Second data array
            
        Returns:
            Correlation coefficient or NaN if calculation fails
            
        Raises:
            None - Returns NaN for any error condition
        """
        try:
            # Convert inputs to numpy arrays and ensure they are numeric
            x = np.asarray(x, dtype=float)
            y = np.asarray(y, dtype=float)
            
            # Check if arrays are empty or contain only non-finite values
            if x.size == 0 or y.size == 0:
                logger.warning("calculate_correlation_safe: Empty arrays provided")
                return np.nan
            
            if not np.isfinite(x).any() or not np.isfinite(y).any():
                logger.warning("calculate_correlation_safe: Arrays contain no finite values")
                return np.nan
            
            # Create mask for valid (finite) values
            mask = np.isfinite(x) & np.isfinite(y)
            
            if np.sum(mask) < 2:
                logger.warning("calculate_correlation_safe: Less than 2 valid data points")
                return np.nan
            
            x_clean = x[mask]
            y_clean = y[mask]
            
            # Check for constant arrays (no variance)
            if np.all(x_clean == x_clean[0]) or np.all(y_clean == y_clean[0]):
                logger.warning("calculate_correlation_safe: One or both arrays are constant (no variance)")
                return np.nan
            
            return np.corrcoef(x_clean, y_clean)[0, 1]
            
        except (ValueError, TypeError) as e:
            logger.error(f"calculate_correlation_safe: Error processing inputs - {str(e)}")
            return np.nan
        except Exception as e:
            logger.error(f"calculate_correlation_safe: Unexpected error - {str(e)}")
            return np.nan
    
    @staticmethod
    def find_nearest_index(array: np.ndarray, value: float) -> int:
        """
        Find the index of the nearest value in an array.
        
        Args:
            array: Input array to search
            value: Target value to find
            
        Returns:
            Index of nearest value
            
        Raises:
            ValueError: If array is empty or invalid
        """
        if not isinstance(array, np.ndarray) or array.size == 0:
            raise ValueError("Array must be a non-empty numpy array")
        
        return int(np.abs(array - value).argmin())
    
    @staticmethod
    def merge_well_data_arrays(
        depth_arrays: List[np.ndarray],
        dt_arrays: List[np.ndarray], 
        dts_arrays: List[np.ndarray],
        rhob_arrays: List[np.ndarray],
        target_arrays: List[np.ndarray],
        well_names: Optional[List[str]] = None
    ) -> Dict[str, np.ndarray]:
        """
        Pure data merging function - returns processed arrays for UI to handle.
        
        Args:
            depth_arrays: List of depth arrays from each well
            dt_arrays: List of DT arrays from each well
            dts_arrays: List of DTS arrays from each well
            rhob_arrays: List of density arrays from each well
            target_arrays: List of target log arrays from each well
            well_names: Optional list of well names for logging
            
        Returns:
            Dict containing merged arrays:
                - depth: Merged depth array
                - dt: Merged DT array
                - dts: Merged DTS array
                - rhob: Merged density array
                - target: Merged target array
                - metadata: Dict with merge statistics
                
        Raises:
            ValueError: If input arrays are invalid or incompatible
        """
        try:
            # Input validation
            arrays_list = [depth_arrays, dt_arrays, dts_arrays, rhob_arrays, target_arrays]
            if not all(isinstance(arr_list, list) for arr_list in arrays_list):
                raise ValueError("All inputs must be lists of numpy arrays")
            
            if not all(len(arr_list) == len(depth_arrays) for arr_list in arrays_list):
                raise ValueError("All input lists must have the same length")
            
            if len(depth_arrays) == 0:
                raise ValueError("Input lists cannot be empty")
            
            # Initialize merged arrays
            merged_depth = []
            merged_dt = []
            merged_dts = []
            merged_rhob = []
            merged_target = []
            
            merge_stats = {
                'wells_processed': 0,
                'total_points': 0,
                'wells_skipped': [],
                'points_per_well': []
            }
            
            # Process each well
            for i, (depth, dt, dts, rhob, target) in enumerate(
                zip(depth_arrays, dt_arrays, dts_arrays, rhob_arrays, target_arrays)
            ):
                well_name = well_names[i] if well_names else f"Well_{i+1}"
                
                try:
                    # Validate individual arrays
                    if not all(isinstance(arr, np.ndarray) for arr in [depth, dt, dts, rhob, target]):
                        logger.warning(f"Skipping {well_name}: Non-numpy array inputs")
                        merge_stats['wells_skipped'].append(well_name)
                        continue
                    
                    if not all(arr.size > 0 for arr in [depth, dt, dts, rhob, target]):
                        logger.warning(f"Skipping {well_name}: Empty arrays")
                        merge_stats['wells_skipped'].append(well_name)
                        continue
                    
                    if not all(arr.shape == depth.shape for arr in [dt, dts, rhob, target]):
                        logger.warning(f"Skipping {well_name}: Inconsistent array shapes")
                        merge_stats['wells_skipped'].append(well_name)
                        continue
                    
                    # Merge data
                    points_added = len(depth)
                    merged_depth.extend(depth)
                    merged_dt.extend(dt)
                    merged_dts.extend(dts)
                    merged_rhob.extend(rhob)
                    merged_target.extend(target)
                    
                    merge_stats['wells_processed'] += 1
                    merge_stats['total_points'] += points_added
                    merge_stats['points_per_well'].append((well_name, points_added))
                    
                    logger.info(f"Merged {points_added} points from {well_name}")
                    
                except Exception as e:
                    logger.error(f"Error processing {well_name}: {str(e)}")
                    merge_stats['wells_skipped'].append(well_name)
                    continue
            
            # Validate merged results
            if not merged_depth:
                raise ValueError("No valid data could be merged from any well")
            
            # Convert to numpy arrays
            result = {
                'depth': np.array(merged_depth),
                'dt': np.array(merged_dt),
                'dts': np.array(merged_dts),
                'rhob': np.array(merged_rhob),
                'target': np.array(merged_target),
                'metadata': merge_stats
            }
            
            logger.info(f"Data merge complete: {merge_stats['wells_processed']} wells, {merge_stats['total_points']} total points")
            
            return result
            
        except Exception as e:
            logger.error(f"Data merge failed: {str(e)}")
            raise ValueError(f"Data merge failed: {str(e)}") from e
    
    @staticmethod
    def validate_array_compatibility(
        arrays: List[np.ndarray],
        array_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Validate that arrays are compatible for analysis.
        
        Args:
            arrays: List of numpy arrays to validate
            array_names: Optional names for arrays (for error reporting)
            
        Returns:
            Dict containing validation results:
                - valid: Boolean indicating if all arrays are compatible
                - errors: List of error messages
                - warnings: List of warning messages
                - statistics: Dict with array statistics
        """
        errors = []
        warnings = []
        statistics = {}
        
        try:
            if not arrays:
                errors.append("No arrays provided for validation")
                return {'valid': False, 'errors': errors, 'warnings': warnings, 'statistics': statistics}
            
            names = array_names if array_names else [f"Array_{i+1}" for i in range(len(arrays))]
            
            # Check array types and basic properties
            for i, (arr, name) in enumerate(zip(arrays, names)):
                if not isinstance(arr, np.ndarray):
                    errors.append(f"{name} is not a numpy array")
                    continue
                
                if arr.size == 0:
                    errors.append(f"{name} is empty")
                    continue
                
                # Calculate statistics
                finite_mask = np.isfinite(arr)
                finite_count = np.sum(finite_mask)
                
                statistics[name] = {
                    'shape': arr.shape,
                    'size': arr.size,
                    'finite_count': finite_count,
                    'finite_percentage': (finite_count / arr.size) * 100 if arr.size > 0 else 0,
                    'dtype': str(arr.dtype)
                }
                
                if finite_count == 0:
                    errors.append(f"{name} contains no finite values")
                elif finite_count < arr.size * 0.5:
                    warnings.append(f"{name} has less than 50% finite values ({finite_count}/{arr.size})")
            
            # Check shape compatibility
            if len(arrays) > 1:
                reference_shape = arrays[0].shape
                for i, (arr, name) in enumerate(zip(arrays[1:], names[1:]), 1):
                    if isinstance(arr, np.ndarray) and arr.shape != reference_shape:
                        errors.append(f"{name} shape {arr.shape} doesn't match reference shape {reference_shape}")
            
            is_valid = len(errors) == 0
            
            return {
                'valid': is_valid,
                'errors': errors,
                'warnings': warnings,
                'statistics': statistics
            }
            
        except Exception as e:
            logger.error(f"Array validation failed: {str(e)}")
            return {
                'valid': False,
                'errors': [f"Validation failed: {str(e)}"],
                'warnings': warnings,
                'statistics': statistics
            }
    
    @staticmethod
    def calculate_array_statistics(
        array: np.ndarray,
        percentiles: Optional[List[float]] = None
    ) -> Dict[str, float]:
        """
        Calculate comprehensive statistics for an array.
        
        Args:
            array: Input numpy array
            percentiles: List of percentiles to calculate (default: [2, 25, 50, 75, 98])
            
        Returns:
            Dict containing statistical measures
        """
        if percentiles is None:
            percentiles = [2, 25, 50, 75, 98]
        
        try:
            # Remove non-finite values
            finite_data = array[np.isfinite(array)]
            
            if len(finite_data) == 0:
                return {
                    'count': 0,
                    'finite_count': 0,
                    'mean': np.nan,
                    'std': np.nan,
                    'min': np.nan,
                    'max': np.nan,
                    **{f'p{p}': np.nan for p in percentiles}
                }
            
            stats = {
                'count': len(array),
                'finite_count': len(finite_data),
                'mean': np.mean(finite_data),
                'std': np.std(finite_data),
                'min': np.min(finite_data),
                'max': np.max(finite_data)
            }
            
            # Add percentiles
            for p in percentiles:
                stats[f'p{p}'] = np.percentile(finite_data, p)
            
            return stats
            
        except Exception as e:
            logger.error(f"Statistics calculation failed: {str(e)}")
            return {
                'count': len(array) if hasattr(array, '__len__') else 0,
                'finite_count': 0,
                'mean': np.nan,
                'std': np.nan,
                'min': np.nan,
                'max': np.nan,
                **{f'p{p}': np.nan for p in percentiles}
            }
```

---

## Phase 3: Configuration Management

### 3.1 Create eei_config.py

**Purpose**: Extract static configuration data with no UI dependencies

```python
# eei_config.py
"""
EEI Analysis Configuration Module

This module contains static configuration data for EEI analysis.
All configuration is centralized here for easy maintenance and modification.

Author: [Your Name]
Created: [Date]
Version: 1.0.0
"""

from typing import Dict, List, Tuple, Any
import numpy as np

# Log keyword mappings for automatic log detection
LOG_KEYWORDS: Dict[str, List[str]] = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC'],
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC'],
    'PHIT': ['PHIT', 'PHID', 'PHI_D'],
    'PHIE': ['PHIE', 'PHIE_D'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO'],
    'SWT': ['SWT', 'SW', 'WATER_SAT'],
    'SWE': ['SWE', 'SWE_D'],
    'DEPTH': ['DEPTH', 'MD', 'MEASURED_DEPTH'],
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS'],
    'FLUID_CODE': ['FLUID_CODE', 'FLUID'],
    'FLUID_PETREL': ['FLUID_PETREL'],
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY', 'LITHO_PETREL'],
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY'],
    'KSOLID': ['KSOLID', 'K_SOLID'],
    'GSOLID': ['GSOLID', 'G_SOLID'],
    'KSAT': ['KSAT', 'K_SATURATION'],
    'GSAT': ['GSAT', 'G_SATURATION'],
    'KDRY': ['KDRY', 'K_DRY'],
    'GDRY': ['GDRY', 'G_DRY'],
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY'],
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90']
}

# Analysis parameter ranges and defaults
ANALYSIS_PARAMS: Dict[str, Dict[str, Any]] = {
    'CPEI': {
        'n_range': {
            'min': 0.1,
            'max': 2.0,
            'step': 0.1,
            'default': np.arange(0.1, 2.1, 0.1)
        },
        'phi_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'description': 'Compressional Poisson Elastic Impedance'
    },
    'PEIL': {
        'n_range': {
            'min': 0.1,
            'max': 2.0,
            'step': 0.1,
            'default': np.arange(0.1, 2.1, 0.1)
        },
        'phi_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'description': 'Poisson Elastic Impedance Log'
    },
    'EEI': {
        'angle_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'k_range': {
            'min': 0.01,
            'max': 1.0,
            'default': 0.25
        },
        'calcmethod_options': [1, 2, 3],
        'calcmethod_default': 3,
        'description': 'Extended Elastic Impedance'
    }
}

# Validation parameters
VALIDATION_PARAMS: Dict[str, Any] = {
    'min_data_points': 10,
    'min_finite_percentage': 50.0,
    'correlation_threshold': 0.01,
    'max_iterations': 10000,
    'convergence_tolerance': 1e-6
}

# UI configuration
UI_CONFIG: Dict[str, Any] = {
    'window_sizes': {
        'calculator': (1000, 800),
        'target_selection': (350, 450),
        'depth_ranges': (600, 500),
        'boundary_selection': (800, 600)
    },
    'colors': {
        'safe_logs': '#00CC00',
        'partial_logs': '#FF6600',
        'calculated_logs': '#0066CC',
        'error': '#FF0000',
        'warning': '#FFA500',
        'info': '#0000FF'
    },
    'fonts': {
        'default': ('Arial', 10),
        'bold': ('Arial', 10, 'bold'),
        'small': ('Arial', 8),
        'code': ('Courier', 10)
    }
}

# File format specifications
FILE_FORMATS: Dict[str, Dict[str, Any]] = {
    'las': {
        'extensions': ['.las'],
        'description': 'Log ASCII Standard files',
        'required_curves': ['DEPTH', 'DT', 'DTS', 'RHOB']
    },
    'excel': {
        'extensions': ['.xls', '.xlsx'],
        'description': 'Excel boundary files',
        'required_columns': ['Well', 'Surface', 'MD']
    }
}

# Error messages and user guidance
ERROR_MESSAGES: Dict[str, str] = {
    'missing_essential_logs': "Essential logs (Vp, Vs, Density) are missing from one or more wells.",
    'invalid_depth_range': "Invalid depth range specified. Top depth must be less than bottom depth.",
    'insufficient_data': "Insufficient valid data points for analysis. Minimum {min_points} required.",
    'calculation_failed': "Calculation failed due to invalid input data or parameters.",
    'correlation_failed': "Correlation calculation failed. Check data quality and compatibility.",
    'optimization_failed': "Parameter optimization failed to converge. Try different parameter ranges."
}

# Success messages
SUCCESS_MESSAGES: Dict[str, str] = {
    'calculation_complete': "Calculation completed successfully.",
    'optimization_complete': "Parameter optimization completed successfully.",
    'data_loaded': "Data loaded successfully from {count} wells.",
    'validation_passed': "All validation checks passed.",
    'export_complete': "Results exported successfully to {filename}."
}

# Default calculation parameters
DEFAULT_PARAMS: Dict[str, Any] = {
    'velocity_conversion_factor': 304800,  # microseconds/ft to m/s
    'percentile_clipping': [2, 98],
    'buffer_factor': 0.02,
    'min_range_factor': 0.01,
    'default_k_value': 0.25,
    'correlation_min_points': 2
}

# Logging configuration
LOGGING_CONFIG: Dict[str, Any] = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'eei_analysis.log',
            'level': 'DEBUG'
        }
    }
}

class EEIConfig:
    """
    Configuration manager for EEI analysis.
    
    Provides centralized access to all configuration parameters
    with validation and type checking.
    """
    
    @staticmethod
    def get_log_keywords() -> Dict[str, List[str]]:
        """Get log keyword mappings."""
        return LOG_KEYWORDS.copy()
    
    @staticmethod
    def get_analysis_params(analysis_type: str) -> Dict[str, Any]:
        """
        Get analysis parameters for specific analysis type.
        
        Args:
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            
        Returns:
            Dict containing analysis parameters
            
        Raises:
            ValueError: If analysis_type is not supported
        """
        if analysis_type.upper() not in ANALYSIS_PARAMS:
            raise ValueError(f"Unsupported analysis type: {analysis_type}")
        
        return ANALYSIS_PARAMS[analysis_type.upper()].copy()
    
    @staticmethod
    def get_validation_params() -> Dict[str, Any]:
        """Get validation parameters."""
        return VALIDATION_PARAMS.copy()
    
    @staticmethod
    def get_ui_config() -> Dict[str, Any]:
        """Get UI configuration."""
        return UI_CONFIG.copy()
    
    @staticmethod
    def get_default_params() -> Dict[str, Any]:
        """Get default calculation parameters."""
        return DEFAULT_PARAMS.copy()
    
    @staticmethod
    def get_error_message(error_key: str, **kwargs) -> str:
        """
        Get formatted error message.
        
        Args:
            error_key: Key for error message
            **kwargs: Format parameters for message
            
        Returns:
            Formatted error message
        """
        if error_key not in ERROR_MESSAGES:
            return f"Unknown error: {error_key}"
        
        try:
            return ERROR_MESSAGES[error_key].format(**kwargs)
        except KeyError:
            return ERROR_MESSAGES[error_key]
    
    @staticmethod
    def get_success_message(success_key: str, **kwargs) -> str:
        """
        Get formatted success message.
        
        Args:
            success_key: Key for success message
            **kwargs: Format parameters for message
            
        Returns:
            Formatted success message
        """
        if success_key not in SUCCESS_MESSAGES:
            return f"Operation completed: {success_key}"
        
        try:
            return SUCCESS_MESSAGES[success_key].format(**kwargs)
        except KeyError:
            return SUCCESS_MESSAGES[success_key]
```

---

## Phase 4: Integration and Testing Strategy

### 4.1 Main File Integration

Update the main file to use the new modules while preserving all UI functionality:

```python
# At the top of a7_load_multilas_EEI_XCOR_PLOT_Final.py
from eei_calculation_engine import EEICalculationEngine
from eei_data_processing import EEIDataProcessor
from eei_config import EEIConfig, LOG_KEYWORDS, ANALYSIS_PARAMS

# Update the global log_keywords variable for backward compatibility
log_keywords = EEIConfig.get_log_keywords()

# Replace the original nanaware_corrcoef function
def nanaware_corrcoef(x, y):
    """Backward compatibility wrapper for correlation calculation."""
    return EEIDataProcessor.calculate_correlation_safe(x, y)

# Replace the original find_nearest_index function  
def find_nearest_index(array, value):
    """Backward compatibility wrapper for nearest index finding."""
    return EEIDataProcessor.find_nearest_index(array, value)
```

### 4.2 Testing Strategy

#### Unit Testing Framework

```python
# tests/test_eei_calculation_engine.py
import unittest
import numpy as np
from eei_calculation_engine import EEICalculationEngine

class TestEEICalculationEngine(unittest.TestCase):
    """Test suite for EEI calculation engine."""
    
    def setUp(self):
        """Set up test data."""
        self.n_points = 100
        self.pvel = np.random.uniform(2000, 4000, self.n_points)
        self.svel = np.random.uniform(1000, 2000, self.n_points)
        self.rhob = np.random.uniform(2.0, 2.8, self.n_points)
        self.target = np.random.uniform(0, 100, self.n_points)
    
    def test_cpei_optimization_valid_inputs(self):
        """Test CPEI optimization with valid inputs."""
        result = EEICalculationEngine.calculate_cpei_optimization(
            self.pvel, self.svel, self.rhob, self.target
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn('correlation_matrix', result)
        self.assertIn('optimal_params', result)
        self.assertIsInstance(result['optimal_params']['n'], (int, float))
        self.assertIsInstance(result['optimal_params']['phi'], (int, float))
    
    def test_cpei_optimization_invalid_inputs(self):
        """Test CPEI optimization with invalid inputs."""
        with self.assertRaises(RuntimeError):
            EEICalculationEngine.calculate_cpei_optimization(
                np.array([]), self.svel, self.rhob, self.target
            )
    
    def test_eei_optimization_valid_inputs(self):
        """Test EEI optimization with valid inputs."""
        result = EEICalculationEngine.calculate_eei_optimization(
            self.pvel, self.svel, self.rhob, self.target
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn('optimal_angle', result)
        self.assertIn('max_correlation', result)
        self.assertIsInstance(result['optimal_angle'], (int, float))
    
    def test_k_value_calculation(self):
        """Test k value calculation methods."""
        k_calc = EEICalculationEngine._determine_k_value(
            self.pvel, self.svel, k_method=1, k_value=None
        )
        self.assertIsInstance(k_calc, float)
        self.assertGreater(k_calc, 0)
        
        k_const = EEICalculationEngine._determine_k_value(
            self.pvel, self.svel, k_method=2, k_value=0.3
        )
        self.assertEqual(k_const, 0.3)

if __name__ == '__main__':
    unittest.main()
```

#### Integration Testing

```python
# tests/test_integration.py
import unittest
import numpy as np
from unittest.mock import Mock, patch
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from eei_calculation_engine import EEICalculationEngine
from eei_data_processing import EEIDataProcessor
from eei_config import EEIConfig

class TestIntegration(unittest.TestCase):
    """Integration tests for EEI analysis modules."""
    
    def setUp(self):
        """Set up test data."""
        self.n_points = 50
        self.pvel = np.random.uniform(2000, 4000, self.n_points)
        self.svel = np.random.uniform(1000, 2000, self.n_points)
        self.rhob = np.random.uniform(2.0, 2.8, self.n_points)
        self.target = np.random.uniform(0, 100, self.n_points)
    
    def test_end_to_end_cpei_workflow(self):
        """Test complete CPEI analysis workflow."""
        # Step 1: Validate data
        validation = EEIDataProcessor.validate_array_compatibility(
            [self.pvel, self.svel, self.rhob, self.target],
            ['pvel', 'svel', 'rhob', 'target']
        )
        self.assertTrue(validation['valid'])
        
        # Step 2: Run optimization
        result = EEICalculationEngine.calculate_cpei_optimization(
            self.pvel, self.svel, self.rhob, self.target
        )
        self.assertIsInstance(result, dict)
        
        # Step 3: Validate results
        self.assertIn('optimal_params', result)
        optimal_params = result['optimal_params']
        self.assertIsInstance(optimal_params['n'], (int, float))
        self.assertIsInstance(optimal_params['phi'], (int, float))
        self.assertIsInstance(optimal_params['max_correlation'], (int, float))
    
    def test_configuration_integration(self):
        """Test configuration module integration."""
        # Test parameter retrieval
        cpei_params = EEIConfig.get_analysis_params('CPEI')
        self.assertIn('n_range', cpei_params)
        self.assertIn('phi_range', cpei_params)
        
        # Test error message formatting
        error_msg = EEIConfig.get_error_message('insufficient_data', min_points=10)
        self.assertIn('10', error_msg)
    
    def test_data_processing_integration(self):
        """Test data processing module integration."""
        # Test correlation calculation
        correlation = EEIDataProcessor.calculate_correlation_safe(
            self.pvel, self.target
        )
        self.assertIsInstance(correlation, (int, float))
        
        # Test array statistics
        stats = EEIDataProcessor.calculate_array_statistics(self.pvel)
        self.assertIn('mean', stats)
        self.assertIn('std', stats)

if __name__ == '__main__':
    unittest.main()
```

### 4.3 Validation and Quality Assurance

#### Backward Compatibility Testing

```python
# tests/test_backward_compatibility.py
import unittest
import numpy as np
from unittest.mock import Mock, patch

class TestBackwardCompatibility(unittest.TestCase):
    """Test backward compatibility with original functions."""
    
    def test_nanaware_corrcoef_compatibility(self):
        """Test that new correlation function matches original behavior."""
        x = np.array([1, 2, 3, 4, 5])
        y = np.array([2, 4, 6, 8, 10])
        
        # Test with new function
        from eei_data_processing import EEIDataProcessor
        new_result = EEIDataProcessor.calculate_correlation_safe(x, y)
        
        # Test with numpy reference
        expected = np.corrcoef(x, y)[0, 1]
        
        self.assertAlmostEqual(new_result, expected, places=10)
    
    def test_function_signatures_preserved(self):
        """Test that wrapper functions maintain original signatures."""
        # This would test that the UI wrapper functions in the main file
        # maintain the same signatures as the original functions
        pass

if __name__ == '__main__':
    unittest.main()
```

---

## Phase 5: Documentation Standards

### 5.1 Module Documentation

Each module should include comprehensive documentation:

```python
"""
Module: eei_calculation_engine.py

Purpose:
    Pure calculation engine for EEI, CPEI, and PEIL optimization analysis.
    Contains stateless calculation methods with no UI dependencies.

Key Features:
    - Unified optimization algorithms for CPEI and PEIL
    - EEI angle optimization with multiple calculation methods
    - Comprehensive input validation and error handling
    - Detailed logging for debugging and monitoring

Dependencies:
    - numpy: Numerical computations
    - logging: Error and debug logging
    - eeimpcalc: Core EEI calculation functions

Usage Example:
    ```python
    from eei_calculation_engine import EEICalculationEngine
    
    # CPEI optimization
    result = EEICalculationEngine.calculate_cpei_optimization(
        pvel, svel, rhob, target
    )
    optimal_n = result['optimal_params']['n']
    optimal_phi = result['optimal_params']['phi']
    ```

Author: [Your Name]
Created: [Date]
Version: 1.0.0
Last Modified: [Date]

Change Log:
    v1.0.0 - Initial implementation with CPEI, PEIL, and EEI optimization
"""
```

### 5.2 Function Documentation Standards

```python
def calculate_cpei_optimization(
    pvel: np.ndarray,
    svel: np.ndarray,
    rhob: np.ndarray,
    target: np.ndarray,
    n_range: Optional[np.ndarray] = None,
    phi_range: Optional[range] = None
) -> Dict[str, Union[np.ndarray, float, int]]:
    """
    Pure CPEI optimization calculation.
    
    This function performs a comprehensive parameter sweep to find the optimal
    n (exponent) and phi (angle) parameters that maximize the correlation
    between CPEI and the target log.
    
    Algorithm:
        1. Validate input arrays for compatibility and data quality
        2. Set default parameter ranges if not provided
        3. Perform nested loop optimization over n and phi values
        4. Calculate CPEI for each parameter combination
        5. Compute correlation with target log
        6. Find parameters that yield maximum correlation
    
    Args:
        pvel (np.ndarray): P-wave velocity array in m/s
            - Must be 1D array with finite values
            - Typical range: 1500-6000 m/s
        svel (np.ndarray): S-wave velocity array in m/s
            - Must be same shape as pvel
            - Typical range: 800-3500 m/s
        rhob (np.ndarray): Bulk density array in g/cm³
            - Must be same shape as pvel
            - Typical range: 1.8-3.0 g/cm³
        target (np.ndarray): Target log array for correlation
            - Must be same shape as pvel
            - Any log type (porosity, saturation, etc.)
        n_range (Optional[np.ndarray]): Range of n values to test
            - Default: np.arange(0.1, 2.1, 0.1)
            - Recommended range: 0.1 to 2.0
        phi_range (Optional[range]): Range of phi values in degrees
            - Default: range(-90, 91)
            - Full range: -90° to +90°
    
    Returns:
        Dict[str, Union[np.ndarray, float, int]]: Optimization results containing:
            - correlation_matrix (np.ndarray): 2D correlation matrix [n_values × phi_values]
            - n_values (np.ndarray): Array of n values tested
            - phi_values (range): Range of phi values tested
            - optimal_params (Dict): Dictionary with:
                - n (float): Optimal n parameter
                - phi (int): Optimal phi parameter in degrees
                - max_correlation (float): Maximum correlation achieved
    
    Raises:
        ValueError: If input arrays are invalid, empty, or incompatible shapes
        RuntimeError: If optimization fails due to calculation errors
    
    Example:
        ```python
        # Basic usage with default parameters
        result = EEICalculationEngine.calculate_cpei_optimization(
            pvel, svel, rhob, porosity_log
        )
        
        # Access results
        optimal_n = result['optimal_params']['n']
        optimal_phi = result['optimal_params']['phi']
        max_corr = result['optimal_params']['max_correlation']
        
        print(f"Optimal CPEI parameters: n={optimal_n:.1f}, phi={optimal_phi}°")
        print(f"Maximum correlation: {max_corr:.4f}")
        
        # Custom parameter ranges
        custom_n = np.arange(0.5, 1.5, 0.1)
        custom_phi = range(-45, 46)
        
        result = EEICalculationEngine.calculate_cpei_optimization(
            pvel, svel, rhob, target, custom_n, custom_phi
        )
        ```
    
    Notes:
        - Function is stateless and thread-safe
        - All calculations use finite values only (NaN values are excluded)
        - Correlation calculation uses complete observations only
        - Progress logging available at INFO level
        - Memory usage scales as O(n_values × phi_values)
    
    Performance:
        - Typical execution time: 1-10 seconds for default ranges
        - Memory usage: ~50MB for default parameter ranges
        - Scales linearly with input array size and parameter range size
    
    See Also:
        - calculate_peil_optimization(): Similar optimization for PEIL
        - calculate_eei_optimization(): EEI angle optimization
        - eeimpcalc.calculate_cpei(): Core CPEI calculation function
    
    References:
        - Connolly, P. (1999). Elastic impedance. The Leading Edge, 18(4), 438-452.
        - Whitcombe, D. N. (2002). Elastic impedance normalization. Geophysics, 67(1), 60-62.
    """
```

---

## Phase 6: Error Handling and Logging Implementation

### 6.1 Comprehensive Error Handling Strategy

```python
# Enhanced error handling patterns for all modules

import logging
from typing import Optional, Dict, Any
from functools import wraps

def handle_calculation_errors(func):
    """
    Decorator for comprehensive error handling in calculation functions.
    
    Provides consistent error handling, logging, and recovery strategies
    across all calculation functions.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            logger.info(f"Starting {func.__name__} with args: {len(args)}, kwargs: {list(kwargs.keys())}")
            result = func(*args, **kwargs)
            logger.info(f"Successfully completed {func.__name__}")
            return result
            
        except ValueError as e:
            logger.error(f"{func.__name__} - Input validation error: {str(e)}")
            raise ValueError(f"Input validation failed in {func.__name__}: {str(e)}") from e
            
        except RuntimeError as e:
            logger.error(f"{func.__name__} - Calculation error: {str(e)}")
            raise RuntimeError(f"Calculation failed in {func.__name__}: {str(e)}") from e
            
        except Exception as e:
            logger.error(f"{func.__name__} - Unexpected error: {str(e)}")
            raise RuntimeError(f"Unexpected error in {func.__name__}: {str(e)}") from e
    
    return wrapper

# Usage example:
@handle_calculation_errors
def calculate_cpei_optimization(self, pvel, svel, rhob, target, n_range=None, phi_range=None):
    # Function implementation
    pass
```

### 6.2 Logging Configuration

```python
# logging_config.py
import logging
import logging.config
from pathlib import Path

def setup_logging(log_level: str = 'INFO', log_file: Optional[str] = None):
    """
    Set up comprehensive logging for EEI analysis modules.
    
    Args:
        log_level: Logging level ('DEBUG', 'INFO', 'WARNING', 'ERROR')
        log_file: Optional log file path
    """
    
    # Create logs directory if it doesn't exist
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'simple': {
                'format': '%(levelname)s - %(message)s'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': log_level,
                'formatter': 'simple',
                'stream': 'ext://sys.stdout'
            }
        },
        'loggers': {
            'eei_calculation_engine': {
                'level': log_level,
                'handlers': ['console'],
                'propagate': False
            },
            'eei_data_processing': {
                'level': log_level,
                'handlers': ['console'],
                'propagate': False
            },
            'eei_config': {
                'level': log_level,
                'handlers': ['console'],
                'propagate': False
            }
        },
        'root': {
            'level': log_level,
            'handlers': ['console']
        }
    }
    
    # Add file handler if log file specified
    if log_file:
        logging_config['handlers']['file'] = {
            'class': 'logging.FileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': log_file,
            'mode': 'a'
        }
        
        # Add file handler to all loggers
        for logger_name in logging_config['loggers']:
            logging_config['loggers'][logger_name]['handlers'].append('file')
        
        logging_config['root']['handlers'].append('file')
    
    logging.config.dictConfig(logging_config)
```

---

## Phase 7: Performance Optimization and State Management

### 7.1 Performance Considerations

```python
# Performance optimization strategies

import numpy as np
from typing import Dict, Any
import time
from functools import lru_cache

class PerformanceOptimizer:
    """
    Performance optimization utilities for EEI calculations.
    """
    
    @staticmethod
    @lru_cache(maxsize=128)
    def cached_parameter_ranges(analysis_type: str, n_min: float, n_max: float, n_step: float,
                               phi_min: int, phi_max: int) -> tuple:
        """
        Cache parameter ranges to avoid repeated array creation.
        
        Args:
            analysis_type: Type of analysis
            n_min, n_max, n_step: N parameter range
            phi_min, phi_max: Phi parameter range
            
        Returns:
            Tuple of (n_values, phi_values)
        """
        n_values = np.arange(n_min, n_max + n_step, n_step)
        phi_values = range(phi_min, phi_max + 1)
        return n_values, phi_values
    
    @staticmethod
    def optimize_array_operations(arrays: list) -> Dict[str, Any]:
        """
        Optimize array operations for better performance.
        
        Args:
            arrays: List of numpy arrays
            
        Returns:
            Dict with optimization statistics
        """
        optimization_stats = {
            'original_memory': sum(arr.nbytes for arr in arrays),
            'optimized_memory': 0,
            'dtype_changes': [],
            'shape_optimizations': []
        }
        
        optimized_arrays = []
        
        for i, arr in enumerate(arrays):
            # Optimize data type if possible
            if arr.dtype == np.float64:
                # Check if float32 precision is sufficient
                if np.allclose(arr, arr.astype(np.float32), rtol=1e-6):
                    optimized_arr = arr.astype(np.float32)
                    optimization_stats['dtype_changes'].append(f"Array {i}: float64 -> float32")
                else:
                    optimized_arr = arr
            else:
                optimized_arr = arr
            
            optimized_arrays.append(optimized_arr)
            optimization_stats['optimized_memory'] += optimized_arr.nbytes
        
        optimization_stats['memory_savings'] = (
            optimization_stats['original_memory'] - optimization_stats['optimized_memory']
        )
        
        return optimization_stats

class CalculationProfiler:
    """
    Profiling utilities for performance monitoring.
    """
    
    def __init__(self):
        self.timings = {}
        self.memory_usage = {}
    
    def profile_function(self, func_name: str):
        """Decorator for profiling function execution."""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    self.timings[func_name] = self.timings.get(func_name, [])
                    self.timings[func_name].append(execution_time)
                    
                    logger.debug(f"{func_name} executed in {execution_time:.4f} seconds")
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    logger.error(f"{func_name} failed after {execution_time:.4f} seconds: {str(e)}")
                    raise
            
            return wrapper
        return decorator
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report."""
        report = {}
        
        for func_name, times in self.timings.items():
            report[func_name] = {
                'call_count': len(times),
                'total_time': sum(times),
                'average_time': np.mean(times),
                'min_time': min(times),
                'max_time': max(times),
                'std_time': np.std(times)
            }
        
        return report
```

### 7.2 State Management Between Modules

```python
# state_manager.py
from typing import Dict, Any, Optional
import threading
from dataclasses import dataclass, field
from datetime import datetime

@dataclass
class AnalysisState:
    """
    Data class to manage analysis state across modules.
    """
    analysis_id: str
    analysis_type: str
    start_time: datetime = field(default_factory=datetime.now)
    status: str = "initialized"
    progress: float = 0.0
    current_step: str = ""
    results: Dict[str, Any] = field(default_factory=dict)
    errors: list = field(default_factory=list)
    warnings: list = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class StateManager:
    """
    Thread-safe state manager for EEI analysis workflow.
    """
    
    def __init__(self):
        self._states: Dict[str, AnalysisState] = {}
        self._lock = threading.Lock()
    
    def create_analysis_state(self, analysis_id: str, analysis_type: str) -> AnalysisState:
        """Create new analysis state."""
        with self._lock:
            state = AnalysisState(analysis_id=analysis_id, analysis_type=analysis_type)
            self._states[analysis_id] = state
            return state
    
    def update_progress(self, analysis_id: str, progress: float, step: str = ""):
        """Update analysis progress."""
        with self._lock:
            if analysis_id in self._states:
                self._states[analysis_id].progress = progress
                self._states[analysis_id].current_step = step
                logger.info(f"Analysis {analysis_id}: {progress:.1f}% - {step}")
    
    def add_result(self, analysis_id: str, key: str, value: Any):
        """Add result to analysis state."""
        with self._lock:
            if analysis_id in self._states:
                self._states[analysis_id].results[key] = value
    
    def add_error(self, analysis_id: str, error: str):
        """Add error to analysis state."""
        with self._lock:
            if analysis_id in self._states:
                self._states[analysis_id].errors.append(error)
                self._states[analysis_id].status = "error"
    
    def get_state(self, analysis_id: str) -> Optional[AnalysisState]:
        """Get analysis state."""
        with self._lock:
            return self._states.get(analysis_id)
    
    def cleanup_state(self, analysis_id: str):
        """Clean up analysis state."""
        with self._lock:
            if analysis_id in self._states:
                del self._states[analysis_id]

# Global state manager instance
state_manager = StateManager()
```

---

## Phase 8: Implementation Timeline and Milestones

### 8.1 Implementation Schedule

| Phase | Duration | Deliverables | Success Criteria |
|-------|----------|--------------|------------------|
| **Phase 1** | Week 1 | eei_calculation_engine.py | All optimization functions extracted, 400 lines saved |
| **Phase 2** | Week 2 | eei_data_processing.py | Data utilities extracted, 300 lines saved |
| **Phase 3** | Week 2 | eei_config.py | Configuration centralized, 100 lines saved |
| **Phase 4** | Week 3 | Integration & Testing | All tests pass, UI functionality preserved |
| **Phase 5** | Week 3 | Documentation | Complete API documentation |
| **Phase 6** | Week 4 | Quality Assurance | Performance validation, error handling verified |

### 8.2 Validation Checkpoints

#### Checkpoint 1: Backend Extraction Validation
- [ ] All calculation functions successfully extracted
- [ ] UI wrapper functions maintain identical interfaces
- [ ] No regression in calculation accuracy
- [ ] Performance maintained or improved

#### Checkpoint 2: Integration Validation
- [ ] All modules import correctly
- [ ] No circular dependencies
- [ ] Configuration system functional
- [ ] Error handling comprehensive

#### Checkpoint 3: Functionality Validation
- [ ] All UI components work identically
- [ ] All analysis workflows complete successfully
- [ ] All plotting functions work correctly
- [ ] All dialog systems functional

#### Checkpoint 4: Quality Validation
- [ ] Code coverage > 80%
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Performance benchmarks met

---

## Phase 9: Maintenance and Future Extensibility

### 9.1 Maintenance Guidelines

#### Code Maintenance
1. **Regular Code Reviews**: Monthly reviews of extracted modules
2. **Dependency Updates**: Quarterly updates of dependencies
3. **Performance Monitoring**: Continuous monitoring of calculation performance
4. **Error Tracking**: Centralized error logging and analysis

#### Documentation Maintenance
1. **API Documentation**: Keep function signatures and examples current
2. **User Guides**: Update user documentation for any interface changes
3. **Change Logs**: Maintain detailed change logs for all modules
4. **Version Control**: Use semantic versioning for all modules

### 9.2 Future Extensibility

#### Module Extension Points
```python
# Example: Adding new analysis types
class EEICalculationEngine:
    @staticmethod
    def calculate_new_analysis_optimization(
        pvel: np.ndarray,
        svel: np.ndarray,
        rhob: np.ndarray,
        target: np.ndarray,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Template for adding new analysis types.
        
        Follow this pattern for consistent interface design.
        """
        # Implementation follows same pattern as existing methods
        pass

# Configuration extension
ANALYSIS_PARAMS['NEW_ANALYSIS'] = {
    'parameter_ranges': {...},
    'description': 'New Analysis Type'
}
```

#### Plugin Architecture
```python
# plugin_interface.py
from abc import ABC, abstractmethod

class AnalysisPlugin(ABC):
    """
    Abstract base class for analysis plugins.
    
    Allows third-party extensions to the EEI analysis system.
    """
    
    @abstractmethod
    def get_name(self) -> str:
        """Return plugin name."""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """Return plugin parameters."""
        pass
    
    @abstractmethod
    def calculate(self, **kwargs) -> Dict[str, Any]:
        """Perform plugin calculation."""
        pass
```

---

## Conclusion

This comprehensive refactoring plan transforms the monolithic 4,942-line script into a clean, modular architecture while preserving 100% of the sophisticated UI functionality. The key achievements include:

### ✅ **Successful Outcomes**
- **🎯 UI Excellence Preserved**: All sophisticated calculator and dialog interfaces remain intact
- **🔧 Backend Optimized**: Clean, testable calculation modules with no UI dependencies
- **📊 Code Reduction**: 20-25% reduction (from 4,942 to ~3,700-3,900 lines)
- **🛡️ Functionality Maintained**: 100% backward compatibility ensured
- **🚀 Architecture Improved**: Clear separation between UI and business logic

### 🔑 **Key Benefits**
1. **Maintainability**: Modular structure enables easier debugging and enhancement
2. **Testability**: Pure calculation functions can be unit tested independently
3. **Reusability**: Backend modules can be used in other applications
4. **Scalability**: Clean interfaces support future extensions
5. **Reliability**: Comprehensive error handling and validation

### 📋 **Implementation Checklist**
- [ ] Phase 1: Extract calculation engine (Week 1)
- [ ] Phase 2: Extract data processing utilities (Week 2)
- [ ] Phase 3: Centralize configuration (Week 2)
- [ ] Phase 4: Integration and testing (Week 3)
- [ ] Phase 5: Documentation completion (Week 3)
- [ ] Phase 6: Quality assurance (Week 4)

The refactored architecture maintains the valuable investment in UI design while creating a solid foundation for future development and maintenance.