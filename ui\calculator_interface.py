# ui/calculator_interface.py
"""
Calculator Interface Module

This module contains the enhanced calculator interface for EEI analysis,
including validation, execution, and error handling functionality.

Author: Refactored from a7_load_multilas_EEI_XCOR_PLOT_Final.py
Created: 2024
Version: 1.0.0
"""

import numpy as np
import tkinter as tk
from tkinter import messagebox, ttk, simpledialog
import logging
import re
from typing import List, Dict, Optional, Any

# Import from other modules
from ui.file_management import analyze_log_availability

# Configure module-specific logger
logger = logging.getLogger(__name__)

class CalculatorInterface:
    """
    Enhanced calculator interface for EEI analysis with comprehensive error handling.
    """

    def __init__(self):
        self.result = {"calculations": None}

    def validate_calculation_inputs(self, las_files: List, calculations: str) -> Dict[str, Any]:
        """
        Validate calculation inputs by checking variable usage against available logs.

        Args:
            las_files: List of lasio.LASFile objects
            calculations: String containing the calculation code

        Returns:
            dict: Validation results with 'valid' boolean and 'error_details' string
        """
        try:
            # Get log availability analysis
            log_analysis = analyze_log_availability(las_files)
            common_logs = set(log_analysis['common_logs'])
            partial_logs = set(log_analysis['partial_logs'].keys())

            # Extract variable names from calculations using regex
            lines = calculations.strip().split('\n')
            input_variables = set()
            output_variables = set()

            logger.info("🔄 Validation: Starting calculation input validation")

            for line in lines:
                logger.info(f"🔄 Validation: Analyzing line: '{line}'")

                # Skip comments
                if line.startswith('#'):
                    continue

                # Look for assignment operations (=, +=, -=, *=, /=)
                if '=' in line and not any(op in line for op in ['==', '!=', '<=', '>=']):
                    # Split on assignment operator
                    if '+=' in line or '-=' in line or '*=' in line or '/=' in line:
                        # Compound assignment - left side is both input and output
                        left_side = line.split('=')[0].strip()
                        right_side = '='.join(line.split('=')[1:]).strip()
                    else:
                        # Regular assignment
                        parts = line.split('=', 1)
                        if len(parts) == 2:
                            left_side = parts[0].strip()
                            right_side = parts[1].strip()
                        else:
                            continue

                    # Extract output variable name (left side of assignment)
                    # Handle array indexing like VAR[0] = ...
                    output_var = re.match(r'^([A-Z_][A-Z0-9_]*)', left_side.upper())
                    if output_var:
                        output_variables.add(output_var.group(1))
                        logger.info(f"🔄 Validation: Found output variable: {output_var.group(1)}")

                    # Extract input variables from right side
                    right_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', right_side.upper()))
                    input_variables.update(right_vars)
                    logger.info(f"🔄 Validation: Found input variables in right side: {right_vars}")
                else:
                    # No assignment, treat all variables as input
                    line_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', line.upper()))
                    input_variables.update(line_vars)
                    logger.info(f"🔄 Validation: Found variables in non-assignment line: {line_vars}")

            # Remove numpy and built-in functions from validation
            builtin_functions = {'NP', 'NUMPY', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN',
                               'NANMEAN', 'NANSTD', 'NANMIN', 'NANMAX', 'WHERE', 'ABS', 'ROUND'}
            input_variables = input_variables - builtin_functions - output_variables

            logger.info(f"🔄 Validation: Final input variables to check: {input_variables}")
            logger.info(f"🔄 Validation: Common logs available: {common_logs}")
            logger.info(f"🔄 Validation: Partial logs available: {partial_logs}")

            # Check if all input variables are available in all wells
            missing_variables = input_variables - common_logs
            partial_variables = input_variables & partial_logs

            if missing_variables or partial_variables:
                error_details = "❌ CALCULATION VALIDATION FAILED\n\n"
                error_details += "Your calculations reference logs that are not available in all wells:\n\n"

                if missing_variables:
                    error_details += "🚫 COMPLETELY MISSING LOGS:\n"
                    for var in sorted(missing_variables):
                        error_details += f"• {var} - Not found in any well\n"
                    error_details += "\n"

                if partial_variables:
                    error_details += "⚠️ PARTIALLY AVAILABLE LOGS:\n"
                    for var in sorted(partial_variables):
                        wells_with_log = log_analysis['partial_logs'][var]['wells']
                        well_count = len(wells_with_log)
                        total_wells = log_analysis['total_wells']
                        error_details += f"• {var} - Available in {well_count}/{total_wells} wells: {', '.join(wells_with_log[:3])}"
                        if well_count > 3:
                            error_details += f" and {well_count - 3} more"
                        error_details += "\n"
                    error_details += "\n"

                error_details += "🔧 HOW TO FIX:\n"
                error_details += "1. Use only logs marked with ✅ (available in all wells)\n"
                error_details += "2. Check the calculator legend before using any logs\n"
                error_details += "3. Remove or replace references to missing/partial logs\n\n"
                error_details += "💡 TIP: The calculator shows which logs are safe to use with ✅ indicators!"

                return {
                    'valid': False,
                    'error_details': error_details
                }

            logger.info("✅ Validation: All input variables are available in all wells")
            return {
                'valid': True,
                'error_details': ''
            }

        except Exception as e:
            error_msg = f"Unexpected error during validation: {str(e)}"
            logger.error(error_msg)
            return {
                'valid': False,
                'error_details': error_msg
            }

    def handle_calculation_error(self, error_details: Dict[str, Any], las_files: List) -> str:
        """
        Show error dialog with recovery options.

        Args:
            error_details: Dictionary containing validation error details
            las_files: List of LAS file objects (for context)

        Returns:
            str: 'retry', 'skip', or 'cancel'
        """
        # Create custom dialog
        root = tk.Tk()
        root.withdraw()

        dialog = tk.Toplevel(root)
        dialog.title("Calculation Validation Error")
        dialog.geometry("600x500")
        dialog.grab_set()

        # Error message
        text_widget = tk.Text(dialog, wrap=tk.WORD, font=('Arial', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, error_details['error_details'])
        text_widget.config(state=tk.DISABLED)

        # Buttons
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        result = {"action": None}

        def on_retry():
            result["action"] = "retry"
            dialog.quit()

        def on_skip():
            result["action"] = "skip"
            dialog.quit()

        def on_cancel():
            result["action"] = "cancel"
            dialog.quit()

        tk.Button(button_frame, text="Retry with Corrections",
                 command=on_retry, bg='lightgreen', width=20).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Skip Calculator",
                 command=on_skip, bg='lightyellow', width=20).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel Analysis",
                 command=on_cancel, bg='lightcoral', width=20).pack(side=tk.LEFT, padx=5)

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        dialog.mainloop()
        dialog.destroy()
        root.destroy()

        return result['action']

    def execute_calculations_safely(self, las_files: List, calculations: str) -> bool:
        """
        Execute calculations with comprehensive error handling.

        Args:
            las_files: List of lasio.LASFile objects
            calculations: String containing the calculation code

        Returns:
            bool: True if successful, False if errors occurred
        """
        error_occurred = False
        error_details = []

        for i, las in enumerate(las_files):
            well_name = las.well.WELL.value

            # Prepare the execution environment
            local_ns = {}

            # Check log availability for this well
            for curve in las.curves.keys():
                local_ns[curve] = np.array(las[curve].data)

            # Make numpy available
            local_ns['np'] = np

            try:
                # Execute the calculations
                exec(calculations, {}, local_ns)
            except NameError as e:
                # Handle missing variable errors with enhanced feedback
                missing_var = str(e).split("'")[1] if "'" in str(e) else "unknown"
                error_details.append(f"Well {well_name}: Missing log '{missing_var}' - This log was marked as ⚠️ (partially available)")
                error_occurred = True
                break
            except Exception as e:
                # Handle other execution errors
                error_details.append(f"Well {well_name}: {str(e)}")
                error_occurred = True
                break

            # Add new variables as curves to the LAS file
            for var_name, data in local_ns.items():
                if var_name not in las.curves.keys() and var_name != 'np':
                    # Check if data is array-like and has the correct length
                    if isinstance(data, np.ndarray) and data.shape[0] == len(las['DEPTH'].data):
                        # Add the new curve
                        las.append_curve(var_name, data)
                        logger.info(f"Added new curve '{var_name}' to well {well_name}")
                    else:
                        # Skip variables that are scalars or arrays of incorrect length
                        continue

        if error_occurred:
            # Show enhanced execution error details
            error_message = "❌ CALCULATION EXECUTION FAILED\n\n"
            error_message += "Your calculations failed because they reference logs that are not available in all wells.\n\n"
            error_message += "SPECIFIC ERRORS:\n"
            for detail in error_details:
                error_message += f"• {detail}\n"

            error_message += "\n🔧 HOW TO FIX:\n"
            error_message += "1. Use only logs marked with ✅ (available in all wells)\n"
            error_message += "2. Check the calculator legend before using any logs\n"
            error_message += "3. Use 'Check Log Availability' button to validate before submitting\n"
            error_message += "4. Remove or replace references to ⚠️ logs\n\n"
            error_message += "💡 TIP: The calculator legend shows which logs are safe to use!"

            messagebox.showerror("Calculator Execution Error", error_message)
            return False

        return True

    def handle_execution_error(self, las_files: List) -> str:
        """
        Handle execution errors with recovery options.

        Args:
            las_files: List of lasio.LASFile objects

        Returns:
            str: 'retry' or 'cancel'
        """
        retry = messagebox.askretrycancel(
            "Calculation Execution Error",
            "An error occurred during calculation execution.\n\n"
            "This could be due to:\n"
            "• Syntax errors in your calculations\n"
            "• Mathematical errors (division by zero, etc.)\n"
            "• Data type mismatches\n\n"
            "Do you want to retry with corrected calculations?"
        )

        return 'retry' if retry else 'cancel'

    def show_calculator_interface(self, las_files: List) -> Optional[str]:
        """
        Enhanced calculator interface showing log availability.

        Args:
            las_files: List of lasio.LASFile objects

        Returns:
            str: Calculation text or None if cancelled
        """
        # Get columns that are present in all LAS files
        common_columns = set(las_files[0].curves.keys())
        for las in las_files[1:]:
            common_columns.intersection_update(las.curves.keys())
        columns = sorted(common_columns)

        if not columns:
            messagebox.showerror("Error", "No common columns found across all LAS files.")
            return None

        root = tk.Tk()
        root.title("Custom Log Calculator - EEI Analysis")
        root.geometry("1000x800")

        # Create PanedWindow
        paned = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)

        # Left Frame: Available variables with availability info
        left_frame = ttk.Frame(paned, width=300)
        paned.add(left_frame, weight=0)

        # Enhanced variable list with availability indicators
        variables_label = tk.Label(left_frame, text="Available Variables:", anchor='w', font=('Arial', 12, 'bold'))
        variables_label.pack(fill=tk.X, pady=(10, 5))

        # Add availability legend with enhanced clarity
        legend_frame = tk.Frame(left_frame)
        legend_frame.pack(fill=tk.X, pady=5)
        tk.Label(legend_frame, text="✅ Available in all wells (SAFE to use)",
                 fg='green', font=('Arial', 9, 'bold')).pack(anchor='w')
        tk.Label(legend_frame, text="⚠️ Available in some wells (WILL CAUSE ERRORS)",
                 fg='red', font=('Arial', 9, 'bold')).pack(anchor='w')

        # Add explanatory note
        note_frame = tk.Frame(left_frame)
        note_frame.pack(fill=tk.X, pady=(0, 5))
        note_text = "Note: Only use ✅ logs for calculations that must work across all wells.\nUsing ⚠️ logs will cause failures in wells where they're missing."
        tk.Label(note_frame, text=note_text, fg='darkblue', font=('Arial', 8),
                 justify=tk.LEFT, wraplength=280).pack(anchor='w')

        # Variables listbox with scrollbar
        listbox_frame = tk.Frame(left_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        variables_listbox = tk.Listbox(listbox_frame, font=('Courier', 10))
        scrollbar_vars = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=variables_listbox.yview)
        variables_listbox.config(yscrollcommand=scrollbar_vars.set)

        # Analyze log availability
        log_analysis = analyze_log_availability(las_files)

        # Populate listbox with availability indicators
        for idx, col in enumerate(columns, start=1):
            if col in log_analysis['common_logs']:
                display_text = f"✅ ({idx:2d}) {col}"
                variables_listbox.insert(tk.END, display_text)
            else:
                wells_with_log = log_analysis['partial_logs'].get(col, {}).get('wells', [])
                well_count = len(wells_with_log)
                total_wells = log_analysis['total_wells']
                display_text = f"⚠️ ({idx:2d}) {col} [{well_count}/{total_wells} wells]"
                variables_listbox.insert(tk.END, display_text)

        variables_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_vars.pack(side=tk.RIGHT, fill=tk.Y)

        # Right Frame: Instructions and Text widget
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)

        # Enhanced instructions with EEI examples and clear safety guidelines
        instructions = (
            "🧮 CUSTOM LOG CALCULATOR FOR EEI ANALYSIS\n\n"
            "⚠️ IMPORTANT: Only use logs marked with ✅ (available in all wells)\n"
            "Using ⚠️ logs will cause calculation failures!\n\n"
            "📋 SAFE CALCULATION EXAMPLES:\n"
            "PHIE_HC = PHIE * (1 - SWE)  # Effective porosity with hydrocarbons\n"
            "VP_VS_RATIO = (304800/DT) / (304800/DTS)  # Vp/Vs ratio\n"
            "AI = RHOB * (304800/DT)  # Acoustic impedance\n"
            "POISSON = 0.5 * ((VP_VS_RATIO**2 - 2) / (VP_VS_RATIO**2 - 1))\n"
            "NORMALIZED_GR = (GR - np.nanmin(GR)) / (np.nanmax(GR) - np.nanmin(GR))\n\n"
            "🔧 AVAILABLE FUNCTIONS:\n"
            "• Numpy: np.log(), np.sqrt(), np.exp(), np.sin(), np.cos(), etc.\n"
            "• Statistics: np.nanmean(), np.nanstd(), np.nanmin(), np.nanmax()\n"
            "• Conditionals: np.where(condition, value_if_true, value_if_false)\n\n"
            "✅ VALIDATION: Use 'Check Log Availability' button before submitting!"
        )

        instruction_label = tk.Label(right_frame, text=instructions,
                                    justify=tk.LEFT, wraplength=650, font=('Arial', 10))
        instruction_label.pack(anchor='w', pady=10, padx=10)

        # Text widget for calculations with scrollbar
        text_frame = tk.Frame(right_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        text = tk.Text(text_frame, width=80, height=20, font=('Courier', 11))
        scrollbar_text = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text.yview)
        text.config(yscrollcommand=scrollbar_text.set)

        text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_text.pack(side=tk.RIGHT, fill=tk.Y)

        # Enhanced button frame
        button_frame = tk.Frame(root)
        button_frame.pack(pady=15)

        result = {"calculations": None}

        def on_submit():
            result["calculations"] = text.get("1.0", tk.END).strip()
            root.quit()

        def on_cancel():
            result["calculations"] = None
            root.quit()

        def on_check_syntax():
            calc_text = text.get("1.0", tk.END).strip()
            if not calc_text:
                messagebox.showwarning("Syntax Check", "Please enter some calculations first.")
                return

            try:
                compile(calc_text, '<string>', 'exec')
                messagebox.showinfo("Syntax Check", "✅ No syntax errors found.")
            except SyntaxError as e:
                messagebox.showerror("Syntax Error", f"❌ Syntax error:\n{e}")

        def on_check_availability():
            calc_text = text.get("1.0", tk.END).strip()
            if not calc_text:
                messagebox.showwarning("Log Availability", "Please enter some calculations first.")
                return

            validation_result = self.validate_calculation_inputs(las_files, calc_text)

            if validation_result['valid']:
                messagebox.showinfo("Log Availability",
                                  "✅ All referenced logs are available in all wells.")
            else:
                messagebox.showwarning("Log Availability",
                                     validation_result['error_details'])

        # Enhanced buttons with better styling
        tk.Button(button_frame, text="Check Syntax",
                  command=on_check_syntax, bg='lightblue', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Check Log Availability",
                  command=on_check_availability, bg='lightyellow', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Submit Calculations",
                  command=on_submit, bg='lightgreen', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel",
                  command=on_cancel, bg='lightcoral', width=18, height=2).pack(side=tk.LEFT, padx=5)

        # Center the window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
        y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
        root.geometry(f"+{x}+{y}")

        root.mainloop()
        root.destroy()

        return result["calculations"]

    def get_calculations_for_eei(self, las_files: List) -> Optional[str]:
        """
        Main function to get calculations for EEI analysis with comprehensive error handling.

        Args:
            las_files: List of lasio.LASFile objects

        Returns:
            str: Calculation text or None if cancelled/failed
        """
        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            logger.info(f"🔄 Calculator: Attempt {attempt}/{max_attempts}")

            # Show calculator interface
            calculations = self.show_calculator_interface(las_files)

            if not calculations:  # User cancelled or submitted empty calculations
                logger.info("🚫 Calculator: User cancelled or submitted empty calculations")
                return None

            # Validate calculations
            validation_result = self.validate_calculation_inputs(las_files, calculations)

            if not validation_result['valid']:
                logger.warning("❌ Calculator: Validation failed")
                action = self.handle_calculation_error(validation_result, las_files)

                if action == 'retry':
                    continue
                elif action == 'skip':
                    logger.info("⏭️ Calculator: User chose to skip calculator")
                    return None
                else:  # cancel
                    logger.info("🚫 Calculator: User cancelled analysis")
                    return None

            # Execute calculations
            if self.execute_calculations_safely(las_files, calculations):
                logger.info("✅ Calculator: Calculations executed successfully")
                return calculations
            else:
                logger.warning("❌ Calculator: Execution failed")
                action = self.handle_execution_error(las_files)

                if action == 'retry':
                    continue
                else:  # cancel
                    logger.info("🚫 Calculator: User cancelled after execution error")
                    return None

        # Max attempts reached
        logger.error(f"❌ Calculator: Maximum attempts ({max_attempts}) reached")
        messagebox.showerror(
            "Calculator Error",
            f"Maximum attempts ({max_attempts}) reached.\n"
            "Please check your calculations and try again later."
        )
        return None


# Create a global instance for backward compatibility
calculator_interface = CalculatorInterface()

# Module-level function for backward compatibility
def get_calculations_for_eei(las_files=None):
    """
    Module-level function for backward compatibility.

    Args:
        las_files: List of lasio.LASFile objects (optional for backward compatibility)

    Returns:
        bool: True if calculations were successful, False otherwise
    """
    if las_files is None:
        # Called without parameters - return success for compatibility
        logger.info("get_calculations_for_eei called without parameters - returning success for compatibility")
        return True
    else:
        # Called with las_files parameter - use the actual implementation
        logger.info("get_calculations_for_eei called with las_files parameter - using actual implementation")
        calc_interface = CalculatorInterface()
        result = calc_interface.get_calculations_for_eei(las_files)
        return result is not None  # Convert string result to boolean for backward compatibility
