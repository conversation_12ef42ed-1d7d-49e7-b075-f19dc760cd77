#!/usr/bin/env python3
"""
Test script to demonstrate the new common boundary selection functionality.

This script shows how the enhanced boundary selection dialog works with
common boundaries that can be applied to all wells simultaneously.
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd
import sys
import os

# Add the current directory to the path so we can import the dialog systems
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.dialog_systems import DialogSystems

def create_sample_data():
    """Create sample boundary data for testing."""
    data = {
        'Well': ['B-G-6', 'B-G-10', 'B-L-1', 'B-L-2.G1', 'B-L-6', 'B-L-9', 'B-L-14', 'B-L-15', 'EB-1'] * 4,
        'Surface': ['E8', 'F19', 'F18-5', 'F19-1'] * 9,
        'MD': [560.51, 3769.85, 553.28, 3585.80, 553.74, 3379.25, 617.89, 3604.87, 
               557.82, 2625.35, 550.66, 2986.37, 555.12, 3878.66, 553.23, 2593.15,
               553.18, 3495.25, 560.51, 3769.85, 553.28, 3585.80, 553.74, 3379.25,
               617.89, 3604.87, 557.82, 2625.35, 550.66, 2986.37, 555.12, 3878.66,
               553.23, 2593.15, 553.18, 3495.25]
    }
    
    return pd.DataFrame(data)

def test_common_boundaries():
    """Test the common boundary selection functionality."""
    print("Testing Common Boundary Selection Dialog")
    print("=" * 50)
    
    # Create sample data
    df = create_sample_data()
    print(f"Created sample data with {len(df)} boundary records")
    print(f"Wells: {df['Well'].unique().tolist()}")
    print(f"Surfaces: {df['Surface'].unique().tolist()}")
    
    # Sample well names (simulating LAS file well names)
    las_well_names = ['B-G-6', 'B-G-10', 'B-L-1', 'B-L-2.G1', 'B-L-6', 'B-L-9', 'B-L-14', 'B-L-15', 'EB-1']
    
    # Create dialog systems instance
    dialog_systems = DialogSystems()
    
    print("\nOpening boundary selection dialog...")
    print("Features to test:")
    print("1. Common Boundaries section at the top")
    print("2. Checkbox to enable common boundary mode")
    print("3. Dropdowns showing surfaces with frequency info")
    print("4. 'Apply to All Wells' button")
    print("5. Status indicators for each well")
    print("6. Enhanced summary on completion")
    
    # Open the dialog
    try:
        result = dialog_systems._select_boundaries_for_all_wells(df, las_well_names)
        
        if result:
            print(f"\nDialog completed successfully!")
            print(f"Selected boundaries for {len(result)} wells:")
            for well, (top, bottom) in result.items():
                print(f"  {well}: {top:.2f} - {bottom:.2f}")
        else:
            print("\nDialog was cancelled or no boundaries were selected.")
            
    except Exception as e:
        print(f"\nError during dialog execution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Create a root window (required for tkinter dialogs)
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    try:
        test_common_boundaries()
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        root.destroy()