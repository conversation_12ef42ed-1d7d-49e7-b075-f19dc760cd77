# ui/file_management.py
import lasio
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import pandas as pd
from tabulate import tabulate
import logging

logger = logging.getLogger(__name__)

# This function is used by other functions in this module, so it's defined first.
def find_default_columns(las, keywords):
    """
    Find the default columns for specific logs based on the provided keywords.
    This version includes detailed logging for troubleshooting.
    """
    default_columns = {}
    for keyword, aliases in keywords.items():
        found = False
        for alias in aliases:
            for curve in las.curves:
                # Case-insensitive comparison
                if alias.upper() == curve.mnemonic.upper():
                    default_columns[keyword] = curve.mnemonic
                    # print(f"Found curve {curve.mnemonic} for alias {alias}") # Keep print for now, or use logger.debug
                    found = True
                    break
            if found:
                break
        if not found:
            default_columns[keyword] = None
            # print(f"Curve for {keyword} not found among aliases {aliases}") # Keep print for now, or use logger.debug
    return default_columns

def load_multiple_las_files():
    """
    Load multiple LAS files and process the data.

    Returns:
    A list of lasio.LASFile objects.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    file_paths = filedialog.askopenfilenames(title="Select LAS files", filetypes=[("LAS files", "*.las")])

    las_files = []
    for file_path in file_paths:
        try:
            las = lasio.read(file_path)
            # Store the file path for reference
            las.file_path = file_path
            las_files.append(las)
            logger.info(f"Loaded file: {file_path}")
        except Exception as e:
            logger.error(f"Error loading file {file_path}: {str(e)}")
            messagebox.showerror("LAS Load Error", f"Error loading file {file_path}:\n{str(e)}")


    return las_files

def validate_essential_logs(las_files, log_keywords):
    """
    Validate that each LAS file contains the essential logs: Vp, Vs, and Density.

    Args:
        las_files: List of lasio.LASFile objects
        log_keywords: Dictionary mapping standardized names to possible mnemonics

    Returns:
        Dictionary with validation results for each file
    """
    validation_results = {}

    for las in las_files:
        well_name = las.well.WELL.value
        file_path = getattr(las, 'file_path', 'Unknown file path')
        file_name = os.path.basename(file_path)

        # Find columns for this well
        columns = find_default_columns(las, log_keywords)

        # Check for essential logs
        missing_logs = []

        # Check for P-wave velocity (either direct or via slowness)
        has_p_wave = False
        if columns.get('P-WAVE') and columns['P-WAVE'] in las.curves:
            has_p_wave = True
        elif columns.get('DT') and columns['DT'] in las.curves:
            has_p_wave = True

        if not has_p_wave:
            missing_logs.append('P-wave velocity (Vp or DT)')

        # Check for S-wave velocity (either direct or via slowness)
        has_s_wave = False
        if columns.get('S-WAVE') and columns['S-WAVE'] in las.curves:
            has_s_wave = True
        elif columns.get('DTS') and columns['DTS'] in las.curves:
            has_s_wave = True

        if not has_s_wave:
            missing_logs.append('S-wave velocity (Vs or DTS)')

        # Check for Density
        has_density = False
        if columns.get('RHOB') and columns['RHOB'] in las.curves:
            has_density = True

        if not has_density:
            missing_logs.append('Density (RHOB)')

        # Store validation result
        validation_results[well_name] = {
            'file_path': file_path,
            'file_name': file_name,
            'missing_logs': missing_logs,
            'is_valid': len(missing_logs) == 0
        }

    return validation_results

def generate_validation_report(validation_results):
    """
    Generate a formatted report of the validation results.

    Args:
        validation_results: Dictionary with validation results from validate_essential_logs()
    """
    report_str = "\n" + "="*80 + "\n"
    report_str += "LAS FILE VALIDATION REPORT\n"
    report_str += "="*80 + "\n"

    valid_files = 0
    total_files = len(validation_results)

    report_str += "\nEssential logs required: P-wave velocity (Vp/DT), S-wave velocity (Vs/DTS), Density (RHOB)\n\n"

    # Prepare data for tabulate
    table_data = []
    for well_name, result in validation_results.items():
        status = "✓ VALID" if result['is_valid'] else "✗ INVALID"
        missing = ", ".join(result['missing_logs']) if result['missing_logs'] else "None"
        table_data.append([well_name, result['file_name'], missing, status])

        if result['is_valid']:
            valid_files += 1

    # Print table
    headers = ["Well Name", "File Name", "Missing Logs", "Status"]
    report_str += tabulate(table_data, headers=headers, tablefmt="grid") + "\n"

    report_str += f"\nSummary: {valid_files}/{total_files} files contain all essential logs.\n"
    report_str += "="*80 + "\n"

    print(report_str) # Keep print for now, or use logger.info

    # Return a summary for potential use elsewhere
    return {
        'valid_files': valid_files,
        'total_files': total_files,
        'invalid_files': total_files - valid_files,
        'report_string': report_str
    }

def categorize_log_curves(las_files):
    """
    Categorize log curves across all LAS files.

    Args:
        las_files: List of lasio.LASFile objects

    Returns:
        Dictionary with categorized log curves
    """
    # Initialize categories
    categories = {}

    # Process each LAS file
    for las in las_files:
        well_name = las.well.WELL.value

        for curve in las.curves:
            mnemonic = curve.mnemonic
            unit = curve.unit
            descr = curve.descr if hasattr(curve, 'descr') else ''

            # Create a standardized key for this curve
            curve_key = mnemonic.upper()

            # Initialize if this is a new curve type
            if curve_key not in categories:
                categories[curve_key] = {
                    'mnemonics': set(),
                    'units': set(),
                    'descriptions': set(),
                    'count': 0,
                    'files': set()
                }

            # Update category information
            categories[curve_key]['mnemonics'].add(mnemonic)
            categories[curve_key]['units'].add(unit)
            categories[curve_key]['descriptions'].add(descr)
            categories[curve_key]['count'] += 1
            categories[curve_key]['files'].add(well_name)

    # Convert sets to lists for easier display
    for key in categories:
        categories[key]['mnemonics'] = list(categories[key]['mnemonics'])
        categories[key]['units'] = list(filter(None, categories[key]['units']))  # Remove empty units
        categories[key]['descriptions'] = list(filter(None, categories[key]['descriptions']))  # Remove empty descriptions
        categories[key]['files'] = list(categories[key]['files'])

    return categories

def display_log_inventory(categories):
    """
    Display the inventory of log curves.

    Args:
        categories: Dictionary with categorized log curves from categorize_log_curves()
    """
    report_str = "\n" + "="*80 + "\n"
    report_str += "LOG CURVE INVENTORY\n"
    report_str += "="*80 + "\n"

    # Sort categories by count (descending)
    sorted_categories = sorted(categories.items(), key=lambda x: x[1]['count'], reverse=True)

    # Prepare data for tabulate
    table_data = []
    for key, info in sorted_categories:
        common_units = ", ".join(info['units'][:2]) if info['units'] else "N/A"  # Show only first 2 units for brevity
        common_desc = ", ".join(info['descriptions'][:1]) if info['descriptions'] else "N/A"  # Show only first description
        file_count = len(info['files'])
        file_list = ", ".join(info['files'][:3])  # Show only first 3 files for brevity
        if file_count > 3:
            file_list += f" and {file_count - 3} more"

        table_data.append([key, info['count'], common_units, common_desc, file_list])

    # Print table
    headers = ["Log Type", "Count", "Common Units", "Description", "Files"]
    report_str += tabulate(table_data, headers=headers, tablefmt="grid") + "\n"

    report_str += f"\nTotal unique log types: {len(categories)}\n"
    report_str += "="*80 + "\n"

    print(report_str) # Keep print for now, or use logger.info

    return categories # Though not used in original, returning it might be useful

def log_available_curves(las):
    """
    Log available curves in the LAS file for diagnostic purposes.
    """
    log_message = f"Available curves for well {las.well.WELL.value}:\n"
    for curve in las.curves:
        log_message += f"  {curve.mnemonic}: {curve.unit}\n"
    logger.debug(log_message) # Changed to logger.debug

def load_boundaries_from_excel(title="Select Excel file with boundary information"):
    """
    Load boundary information from an Excel file.

    Args:
        title: The title to display in the file dialog

    Returns:
        DataFrame containing well names, surface names, and measured depths
        or None if no file was selected or an error occurred.
    """
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")]
    )

    if not file_path:
        logger.info("No Excel file selected for boundaries.")
        return None

    try:
        # Load the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            err_msg = (f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                       f"Please ensure the file contains columns named: {', '.join(required_columns)}")
            messagebox.showerror("Missing Columns", err_msg)
            logger.error(err_msg)
            return None

        # Basic validation
        if df.empty:
            err_msg = "The Excel file contains no data."
            messagebox.showerror("Empty File", err_msg)
            logger.error(err_msg)
            return None

        logger.info(f"Successfully loaded boundary data from {file_path}")
        logger.info(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        err_msg = f"An error occurred while loading the Excel file:\n{str(e)}"
        messagebox.showerror("Error Loading File", err_msg)
        logger.error(f"Error loading Excel file: {str(e)}")
        return None

def filter_excel_data_for_las_wells(df, las_files):
    """
    Filter Excel data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths
        las_files: List of LAS file objects

    Returns:
        Filtered DataFrame containing only boundaries for wells in the LAS files
    """
    if df is None:
        return None

    # Get the list of well names from the LAS files
    las_well_names = [las.well.WELL.value for las in las_files]

    # Filter the DataFrame to only include rows where the Well column value is in las_well_names
    filtered_df = df[df['Well'].isin(las_well_names)]

    # Check if we have any matching wells
    if filtered_df.empty:
        logger.warning("No matching wells found in Excel file. Excel wells: "
                       f"{', '.join(df['Well'].unique())}. LAS wells: {', '.join(las_well_names)}")
        messagebox.showwarning("No Matching Wells", "No wells in the Excel boundary file match the loaded LAS files.")
        return None

    # Log the filtering results
    original_well_count = df['Well'].nunique()
    filtered_well_count = filtered_df['Well'].nunique()
    logger.info(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    logger.info(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df

def load_excel_depth_ranges(las_files):
    """
    Prompt the user to load an Excel file with depth ranges at the beginning of the program.
    Filter the data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        las_files: List of LAS file objects

    Returns:
        DataFrame containing well names, surface names, and measured depths (filtered for LAS wells)
        or None if no file was selected, the user canceled, or an error occurred.
    """
    # Ask user if they want to load an Excel file with depth ranges
    root = tk.Tk()
    root.withdraw()

    load_excel = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'."
    )

    if not load_excel:
        logger.info("User chose not to load Excel file with depth ranges at this time.")
        return None

    # Load the Excel file
    df = load_boundaries_from_excel("Select Excel file with depth ranges")

    # Filter the data to only include boundaries for wells in the LAS files
    return filter_excel_data_for_las_wells(df, las_files)

def analyze_log_availability(las_files):
    """
    Analyze which logs are available across all wells.

    Args:
        las_files: List of lasio.LASFile objects

    Returns:
        dict: {
            'common_logs': list,      # Available in all wells
            'partial_logs': dict,     # Available in some wells
            'total_wells': int        # Total number of wells
        }
    """
    all_logs = {}
    well_count = len(las_files)

    # Count log occurrences across wells
    for las in las_files:
        well_name = las.well.WELL.value
        for curve_name in las.curves.keys():
            if curve_name not in all_logs:
                all_logs[curve_name] = {'wells': [], 'count': 0}
            all_logs[curve_name]['wells'].append(well_name)
            all_logs[curve_name]['count'] += 1

    # Categorize logs
    common_logs = [log for log, info in all_logs.items() if info['count'] == well_count]
    partial_logs = {log: info for log, info in all_logs.items()
                   if 0 < info['count'] < well_count}

    return {
        'common_logs': sorted(common_logs),
        'partial_logs': partial_logs,
        'total_wells': well_count
    }

def log_available_curves(las):
    """
    Log available curves in the LAS file for diagnostic purposes.
    """
    log_message = f"Available curves for well {las.well.WELL.value}:\n"
    for curve in las.curves:
        log_message += f"  {curve.mnemonic}: {curve.unit}\n"
    logger.debug(log_message)