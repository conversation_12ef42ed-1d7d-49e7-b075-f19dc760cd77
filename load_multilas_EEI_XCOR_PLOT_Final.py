# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 11:15:20 2024

@author: devri.agustianto
"""

# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]

import numpy as np
import lasio
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import matplotlib.pyplot as plt  # Still needed for some remaining plotting code
from eeimpcalc import eeimpcalc, calculate_cpei, calculate_peil  # Import the external module
import pandas as pd
from scipy import stats  # Still needed for regression analysis
import logging  # Import for comprehensive error logging

# Import new modular components
from eei_calculation_engine import EEICalculationEngine
from eei_data_processing import EEIDataProcessor
from eei_config import LOG_KEYWORDS
from ui.helper_functions import safe_format_float, safe_format_parameter_string, validate_cpei_peil_inputs
from ui.file_management import (
    load_multiple_las_files, validate_essential_logs, generate_validation_report,
    categorize_log_curves, display_log_inventory, find_default_columns,
    analyze_log_availability, load_boundaries_from_excel, filter_excel_data_for_las_wells,
    load_excel_depth_ranges, log_available_curves
)
# Import workflow orchestration module (Phase 3B)
from ui.workflow_orchestration import (
    individual_well_analysis as _individual_well_analysis,
    merged_well_analysis as _merged_well_analysis,
    run_eei_analysis as _run_eei_analysis
)
from ui.calculator_interface import calculator_interface
from ui.dialog_systems import dialog_systems
from ui.plotting_components import (
    plot_eei_vs_target, calculate_global_percentiles_for_axis_limits,
    calculate_optimal_crossplot_limits, plot_correlation_vs_angle,
    plot_correlation_heatmap, plot_summary_chart
)

# Configure logging for debugging NoneType format string errors
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HELPER FUNCTIONS FOR SAFE STRING FORMATTING AND ERROR HANDLING
# (Functions moved to ui/helper_functions.py)

# Calculator functions moved to ui/calculator_interface.py

# Legacy function wrapper for backward compatibility
def validate_calculation_inputs(las_files, calculation_text):
    """Legacy wrapper for backward compatibility."""
    from ui.calculator_interface import CalculatorInterface
    calc_interface = CalculatorInterface()
    return calc_interface.validate_calculation_inputs(las_files, calculation_text)

def handle_calculation_error(error_details, las_files):
    """Legacy wrapper for backward compatibility."""
    from ui.calculator_interface import CalculatorInterface
    calc_interface = CalculatorInterface()
    return calc_interface.handle_calculation_error(error_details, las_files)



def execute_calculations_safely(las_files, calculations):
    """Legacy wrapper for backward compatibility."""
    from ui.calculator_interface import CalculatorInterface
    calc_interface = CalculatorInterface()
    return calc_interface.execute_calculations_safely(las_files, calculations)

def handle_execution_error(las_files):
    """Legacy wrapper for backward compatibility."""
    from ui.calculator_interface import CalculatorInterface
    calc_interface = CalculatorInterface()
    return calc_interface.handle_execution_error(las_files)

def show_calculator_interface(las_files):
    """Legacy wrapper for backward compatibility."""
    from ui.calculator_interface import CalculatorInterface
    calc_interface = CalculatorInterface()
    return calc_interface.show_calculator_interface(las_files)

def get_calculations_for_eei(las_files):
    """Legacy wrapper for backward compatibility."""
    # Import the CalculatorInterface class and create an instance
    from ui.calculator_interface import CalculatorInterface
    calc_interface = CalculatorInterface()
    result = calc_interface.get_calculations_for_eei(las_files)
    return result is not None  # Convert string result to boolean for backward compatibility

# COMPLATION OF FUNCTION
# Use centralized log keywords from configuration
log_keywords = LOG_KEYWORDS

# load_multiple_las_files function moved to ui/file_management.py

# validate_essential_logs function moved to ui/file_management.py

# generate_validation_report function moved to ui/file_management.py

# categorize_log_curves function moved to ui/file_management.py

# display_log_inventory function moved to ui/file_management.py

# log_available_curves function moved to ui/file_management.py

# find_default_columns function moved to ui/file_management.py


def nanaware_corrcoef(x, y):
    """
    Backward compatibility wrapper for correlation calculation.
    Delegates to the modular EEIDataProcessor.calculate_correlation_safe().
    """
    return EEIDataProcessor.calculate_correlation_safe(x, y)

def find_nearest_index(array, value):
    """
    Backward compatibility wrapper for nearest index finding.
    Delegates to the modular EEIDataProcessor.find_nearest_index().
    """
    return EEIDataProcessor.find_nearest_index(array, value)

def merge_well_data(las_files, columns, target_log, depth_ranges):
    """
    Merge data from multiple wells into a single array.
    Includes robust validation and logging.
    """
    merged_depth, merged_dt, merged_dts, merged_rhob, merged_target = [], [], [], [], []

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges.get(well_name, (None, None))

        if top_depth is None or bottom_depth is None:
            logger.warning(f"Well {well_name}: Skipping merge due to missing depth range.")
            continue

        # Extract data with existence checks
        curve_keys = {key.upper() for key in las.curves.keys()} # Use uppercase for comparison

        depth_key_generic = 'DEPTH'
        dt_key_generic = 'DT'
        dts_key_generic = 'DTS'
        rhob_key_generic = 'RHOB'

        depth_key_actual = columns.get(depth_key_generic)
        dt_key_actual = columns.get(dt_key_generic)
        dts_key_actual = columns.get(dts_key_generic)
        rhob_key_actual = columns.get(rhob_key_generic)

        # Target log can be a generic keyword or an actual mnemonic
        target_key_actual = columns.get(target_log.upper()) or target_log # Check columns first, then direct

        # Validate essential log keys from `columns` dictionary
        if not depth_key_actual or depth_key_actual.upper() not in curve_keys:
            logger.warning(f"Well {well_name}: Skipping merge due to missing or unmapped DEPTH log ('{depth_key_actual}').")
            continue
        if not dt_key_actual or dt_key_actual.upper() not in curve_keys or \
           not dts_key_actual or dts_key_actual.upper() not in curve_keys or \
           not rhob_key_actual or rhob_key_actual.upper() not in curve_keys:
            logger.warning(f"Well {well_name}: Skipping merge due to missing or unmapped essential logs (DT: '{dt_key_actual}', DTS: '{dts_key_actual}', or RHOB: '{rhob_key_actual}').")
            continue

        # Validate target log key
        if not target_key_actual or target_key_actual.upper() not in curve_keys:
            # If target_log itself (original case) is in curves, use it
            if target_log in las.curves.keys():
                target_key_actual = target_log
                logger.info(f"Well {well_name}: Using direct mnemonic '{target_log}' for target log as mapped key '{columns.get(target_log.upper())}' not found.")
            else:
                logger.warning(f"Well {well_name}: Skipping merge due to missing or unmapped target log '{target_log}' (tried '{target_key_actual}').")
                continue

        depth = np.array(las[depth_key_actual].data)
        dt = np.array(las[dt_key_actual].data)
        dts = np.array(las[dts_key_actual].data)
        rhob = np.array(las[rhob_key_actual].data)
        target = np.array(las[target_key_actual].data)

        # Validate array lengths and content
        if not all(hasattr(arr, 'size') and arr.size > 0 for arr in [depth, dt, dts, rhob, target]):
            logger.warning(f"Well {well_name}: Skipping merge due to empty data arrays for one or more required logs.")
            continue

        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        if top_index is None or bottom_index is None : # find_nearest_index could return None if array is empty, though checked above
             logger.warning(f"Well {well_name}: Skipping merge due to invalid top/bottom indices (possibly empty depth array after initial check).")
             continue

        if top_index > bottom_index: # Ensure correct order
            logger.info(f"Well {well_name}: Top depth index ({top_index} for {top_depth}) is after bottom depth index ({bottom_index} for {bottom_depth}). Swapping them.")
            top_index, bottom_index = bottom_index, top_index

        if top_index == bottom_index and depth[top_index:bottom_index+1].size == 0 : # Handles single point interval correctly
             logger.warning(f"Well {well_name}: Skipping merge as depth interval resulted in zero data points ({top_depth} to {bottom_depth}).")
             continue


        # Extend merged arrays
        merged_depth.extend(depth[top_index:bottom_index+1])
        merged_dt.extend(dt[top_index:bottom_index+1])
        merged_dts.extend(dts[top_index:bottom_index+1])
        merged_rhob.extend(rhob[top_index:bottom_index+1])
        merged_target.extend(target[top_index:bottom_index+1])
        logger.info(f"Well {well_name}: Successfully merged data for depth range {top_depth}-{bottom_depth} (indices {top_index}-{bottom_index}). Added {len(depth[top_index:bottom_index+1])} data points.")


    # Convert to numpy arrays and check for emptiness
    if not merged_depth:
        logger.error("Merged data is empty. No valid data available for analysis from any well.")
        return None, None, None, None, None

    logger.info(f"Successfully merged data from contributing wells. Total merged data points: {len(merged_depth)}.")
    return (np.array(merged_depth), np.array(merged_dt), np.array(merged_dts),
            np.array(merged_rhob), np.array(merged_target))

# get_analysis_type_and_parameters function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def get_analysis_type_and_parameters():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_analysis_type_and_parameters()

def calculate_eei_optimum_angle(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth, calcmethod, k_method=1, k_value=None):
    """
    UI wrapper for EEI optimization - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """

    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    try:
        # Convert to velocities (existing code preserved)
        pvel = 304800 / dt
        svel = 304800 / dts

        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_eei_optimization(
            pvel, svel, rhob, target, calcmethod, k_method, k_value
        )

        # Extract results for UI display (preserving existing format)
        optimum_angle = results['optimal_angle']
        max_correlation = results['max_correlation']
        angles = results['angles']
        correlations = results['correlations']
        k_used = results['k_used']

        # ALL existing result processing and UI updates remain identical
        logger.info(f"Optimization completed. Optimum angle: {optimum_angle}°, Max correlation: {safe_format_float(max_correlation, precision=4)}")
        logger.info(f"K value used: {safe_format_float(k_used, precision=4)}")

        return optimum_angle, max_correlation, angles, correlations.tolist()

    except Exception as e:
        logger.error(f"Error in calculate_eei_optimum_angle: {str(e)}")
        return None, None, range(-90, 91), [np.nan] * 181

def calculate_eei_optimum_angle_merged(depth, dt, dts, rhob, target, calcmethod, k_method=1, k_value=None):
    """
    UI wrapper for EEI optimization on merged data - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """
    try:
        # Convert to velocities (existing code preserved)
        pvel = 304800 / dt
        svel = 304800 / dts

        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_eei_optimization(
            pvel, svel, rhob, target, calcmethod, k_method, k_value
        )

        # Extract results for UI display (preserving existing format)
        optimum_angle = results['optimal_angle']
        max_correlation = results['max_correlation']
        angles = results['angles']
        correlations = results['correlations']
        k_used = results['k_used']

        # ALL existing result processing and UI updates remain identical
        logger.info(f"Optimization completed. Optimum angle: {optimum_angle}°, Max correlation: {safe_format_float(max_correlation, precision=4)}")
        logger.info(f"K value used (merged): {safe_format_float(k_used, precision=4)}")

        return optimum_angle, max_correlation, angles, correlations.tolist()

    except Exception as e:
        logger.error(f"Error in calculate_eei_optimum_angle_merged: {str(e)}")
        return None, None, range(-90, 91), [np.nan] * 181

def calculate_cpei_optimum_parameters(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth):
    """
    UI wrapper for CPEI optimization - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """

    # ALL existing UI code remains identical:
    # - Progress dialogs
    # - Error handling
    # - User feedback
    # - Data extraction and validation

    print(f"Starting CPEI optimization for target log: {target_log_actual_mnemonic}")

    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert to velocities (existing code preserved)
    pvel = 304800 / dt
    svel = 304800 / dts

    try:
        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_cpei_optimization(
            pvel, svel, rhob, target
        )

        # Extract results for UI display (preserving existing format)
        optimal_n = results['optimal_params']['n']
        optimal_phi = results['optimal_params']['phi']
        max_correlation = results['optimal_params']['max_correlation']
        n_values = results['n_values']
        phi_values = results['phi_values']
        correlation_matrix = results['correlation_matrix']

        # ALL existing result processing and UI updates remain identical
        print(f"CPEI optimization complete:")
        print(f"  Optimal n: {safe_format_float(optimal_n, precision=1, default='N/A')}")
        print(f"  Optimal phi: {optimal_phi}°")
        print(f"  Maximum correlation: {safe_format_float(max_correlation, precision=4, default='N/A')}")

        return optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix

    except Exception as e:
        # Enhanced error handling with backend error context
        logger.error(f"CPEI optimization failed: {str(e)}")
        print(f"Error in CPEI optimization: {str(e)}")
        return None, None, None, np.arange(0.1, 2.1, 0.1), range(-90, 91), np.full((20, 181), np.nan)

def calculate_peil_optimum_parameters(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth):
    """
    UI wrapper for PEIL optimization - preserves all existing UI functionality.

    This function maintains the exact same interface and behavior as the original,
    but delegates pure calculation to the backend engine.
    """

    print(f"Starting PEIL optimization for target log: {target_log_actual_mnemonic}")

    # Extract and prepare data (existing code preserved)
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find depth indices (existing code preserved)
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice arrays (existing code preserved)
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert to velocities (existing code preserved)
    pvel = 304800 / dt
    svel = 304800 / dts

    try:
        # Delegate pure calculation to backend engine
        results = EEICalculationEngine.calculate_peil_optimization(
            pvel, svel, rhob, target
        )

        # Extract results for UI display (preserving existing format)
        optimal_n = results['optimal_params']['n']
        optimal_phi = results['optimal_params']['phi']
        max_correlation = results['optimal_params']['max_correlation']
        n_values = results['n_values']
        phi_values = results['phi_values']
        correlation_matrix = results['correlation_matrix']

        # ALL existing result processing and UI updates remain identical
        print(f"PEIL optimization complete:")
        print(f"  Optimal n: {safe_format_float(optimal_n, precision=1, default='N/A')}")
        print(f"  Optimal phi: {optimal_phi}°")
        print(f"  Maximum correlation: {safe_format_float(max_correlation, precision=4, default='N/A')}")

        return optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix

    except Exception as e:
        # Enhanced error handling with backend error context
        logger.error(f"PEIL optimization failed: {str(e)}")
        print(f"Error in PEIL optimization: {str(e)}")
        return None, None, None, np.arange(0.1, 2.1, 0.1), range(-90, 91), np.full((20, 181), np.nan)

def calculate_eei(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, angle, calcmethod, k_method=1, k_value=None):
    """
    Calculate EEI using the given angle and clip results based on percentiles.
    Uses actual mnemonics specific to the 'las' file.
    actual_base_log_mnemonics is a dict: {'DEPTH': 'actual_depth_name', 'DT': 'actual_dt_name', ...}
    target_log_actual_mnemonic is the string name of the target curve.
    vcl_actual_mnemonic is the string name of the VCL curve, or None.
    """
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find nearest indices for top and bottom depths
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    # Ensure top_index is smaller than bottom_index
    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice the arrays using the indices
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / dt  # Convert microseconds/ft to m/s
    svel = 304800 / dts  # Convert microseconds/ft to m/s


    # Determine k value based on method selected
    if k_method == 1:
        # Calculate k from velocity ratio
        velocity_ratio = svel / pvel
        k = np.nanmean(velocity_ratio**2)

        # Validate calculated k value
        if k is None or not np.isfinite(k) or k <= 0:
            logger.warning(f"Invalid calculated k value: {k}, using default k=0.25")
            k = 0.25  # Default reasonable k value

        logger.debug(f"Calculated k value from logs (final EEI): {safe_format_float(k, precision=4)}")
    else:
        k = k_value

        # Validate provided k value
        if k is None or not np.isfinite(k) or k <= 0:
            logger.warning(f"Invalid provided k value: {k}, using default k=0.25")
            k = 0.25  # Default reasonable k value

        logger.debug(f"Using constant k value (final EEI): {safe_format_float(k, precision=4)}")

    # Calculate EEI with error handling
    try:
        eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)

        # Validate EEI calculation result
        if eei is None or not hasattr(eei, '__len__'):
            logger.error("EEI calculation returned invalid result")
            return None

    except Exception as e:
        logger.error(f"Error calculating EEI: {str(e)}")
        return None

     # Step 1: Min-Max Normalization of EEI
    eei_min = np.nanmin(eei)
    eei_max = np.nanmax(eei)

    # Avoid division by zero if min and max are the same
    if eei_max != eei_min:
        norm_eei = (eei - eei_min) / (eei_max - eei_min)
    else:
        norm_eei = eei  # No scaling needed if all values are the same

    # Step 2: Scale the normalized EEI to match the target log range
    target_min = np.nanmin(target)
    target_max = np.nanmax(target)

    normalized_eei = norm_eei * (target_max - target_min) + target_min

    # Get VOL_WETCLAY data
    vol_wetclay = None
    if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
        vol_wetclay = np.array(las[vcl_actual_mnemonic].data)[top_index:bottom_index+1]

    # Warning for missing VCL is better handled where find_default_columns is called.
    # if vol_wetclay is None:
    #     print(f"Info: VOL_WETCLAY (mnemonic: {vcl_actual_mnemonic}) not available or not found for well {las.well.WELL.value} in calculate_eei. Proceeding without VOL_WETCLAY data.")

    # Remove NaN and Inf values, and ensure consistent lengths
    mask = np.isfinite(depth) & np.isfinite(target) & np.isfinite(normalized_eei)
    if vol_wetclay is not None:
        mask = mask & np.isfinite(vol_wetclay)

    depth = depth[mask]
    target = target[mask]
    normalized_eei = normalized_eei[mask]
    if vol_wetclay is not None:
        vol_wetclay = vol_wetclay[mask]

    # Check if arrays are empty or contain only NaNs
    if depth.size == 0 or target.size == 0 or normalized_eei.size == 0:
        print(f"No valid data to process for well {las.well.WELL.value}. Skipping EEI calculation.")
        return None, None, None, None

    # Use np.nanpercentile to compute percentiles while ignoring NaNs
    eei_low, eei_high = np.nanpercentile(normalized_eei, [2, 98])
    target_low, target_high = np.nanpercentile(target, [2, 98])

    # Clip normalized_eei and target based on 2nd and 98th percentiles
    normalized_eei = np.clip(normalized_eei, eei_low, eei_high)
    target = np.clip(target, target_low, target_high)

    return depth, target, normalized_eei, vol_wetclay

def calculate_cpei_for_plotting(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, optimal_n, optimal_phi):
    """
    Calculate CPEI using the optimal parameters and prepare data for plotting.
    Similar to calculate_eei but uses calculate_cpei instead of eeimpcalc.
    """
    try:
        depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
        dt = np.array(las[actual_base_log_mnemonics['DT']].data)
        dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
        rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
        target = np.array(las[target_log_actual_mnemonic].data)

        # Handle VCL (optional)
        vol_wetclay = None
        if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
            vol_wetclay = np.array(las[vcl_actual_mnemonic].data)

        # Convert slowness to velocity
        pvel = 304800 / dt  # Convert microseconds/ft to m/s
        svel = 304800 / dts  # Convert microseconds/ft to m/s

        # Validate input arrays
        if not all(np.isfinite(arr).any() for arr in [pvel, svel, rhob, target]):
            logger.error("One or more input arrays contain no finite values for CPEI plotting")
            return None, None, None, None

        # Calculate CPEI with error handling
        cpei = calculate_cpei(pvel, svel, rhob, optimal_n, optimal_phi)

        if cpei is None or not hasattr(cpei, '__len__'):
            logger.error("CPEI calculation returned invalid result for plotting")
            return None, None, None, None

        # Min-Max Normalization of CPEI
        cpei_min = np.nanmin(cpei)
        cpei_max = np.nanmax(cpei)

        if cpei_max != cpei_min:
            norm_cpei = (cpei - cpei_min) / (cpei_max - cpei_min)
        else:
            norm_cpei = cpei

        # Find nearest indices for depth range
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice arrays to depth range
        depth = depth[top_index:bottom_index+1]
        target = target[top_index:bottom_index+1]
        normalized_cpei = norm_cpei[top_index:bottom_index+1]
        if vol_wetclay is not None:
            vol_wetclay = vol_wetclay[top_index:bottom_index+1]

        # Check if arrays are empty
        if depth.size == 0 or target.size == 0 or normalized_cpei.size == 0:
            print(f"No valid data to process for CPEI plotting in well {las.well.WELL.value}.")
            return None, None, None, None

        # Clip based on percentiles
        cpei_low, cpei_high = np.nanpercentile(normalized_cpei, [2, 98])
        target_low, target_high = np.nanpercentile(target, [2, 98])

        normalized_cpei = np.clip(normalized_cpei, cpei_low, cpei_high)
        target = np.clip(target, target_low, target_high)

        return depth, target, normalized_cpei, vol_wetclay

    except Exception as e:
        logger.error(f"Error in calculate_cpei_for_plotting: {str(e)}")
        return None, None, None, None

def calculate_peil_for_plotting(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, optimal_n, optimal_phi):
    """
    Calculate PEIL using the optimal parameters and prepare data for plotting.
    Similar to calculate_eei but uses calculate_peil instead of eeimpcalc.
    """
    try:
        depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
        dt = np.array(las[actual_base_log_mnemonics['DT']].data)
        dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
        rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
        target = np.array(las[target_log_actual_mnemonic].data)

        # Handle VCL (optional)
        vol_wetclay = None
        if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
            vol_wetclay = np.array(las[vcl_actual_mnemonic].data)

        # Convert slowness to velocity
        pvel = 304800 / dt  # Convert microseconds/ft to m/s
        svel = 304800 / dts  # Convert microseconds/ft to m/s

        # Validate input arrays
        if not all(np.isfinite(arr).any() for arr in [pvel, svel, rhob, target]):
            logger.error("One or more input arrays contain no finite values for PEIL plotting")
            return None, None, None, None

        # Calculate PEIL with error handling
        peil = calculate_peil(pvel, svel, rhob, optimal_n, optimal_phi)

        if peil is None or not hasattr(peil, '__len__'):
            logger.error("PEIL calculation returned invalid result for plotting")
            return None, None, None, None

        # Min-Max Normalization of PEIL
        peil_min = np.nanmin(peil)
        peil_max = np.nanmax(peil)

        if peil_max != peil_min:
            norm_peil = (peil - peil_min) / (peil_max - peil_min)
        else:
            norm_peil = peil

        # Find nearest indices for depth range
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice arrays to depth range
        depth = depth[top_index:bottom_index+1]
        target = target[top_index:bottom_index+1]
        normalized_peil = norm_peil[top_index:bottom_index+1]
        if vol_wetclay is not None:
            vol_wetclay = vol_wetclay[top_index:bottom_index+1]

        # Check if arrays are empty
        if depth.size == 0 or target.size == 0 or normalized_peil.size == 0:
            print(f"No valid data to process for PEIL plotting in well {las.well.WELL.value}.")
            return None, None, None, None

        # Clip based on percentiles
        peil_low, peil_high = np.nanpercentile(normalized_peil, [2, 98])
        target_low, target_high = np.nanpercentile(target, [2, 98])

        normalized_peil = np.clip(normalized_peil, peil_low, peil_high)
        target = np.clip(target, target_low, target_high)

        return depth, target, normalized_peil, vol_wetclay

    except Exception as e:
        logger.error(f"Error in calculate_peil_for_plotting: {str(e)}")
        return None, None, None, None

# calculate_optimal_crossplot_limits function moved to ui/plotting_components.py
def calculate_optimal_crossplot_limits_REMOVED(x_data, y_data, correlation=None, global_x_min=None, global_x_max=None, global_y_min=None, global_y_max=None, well_name="Unknown"):
    """
    Calculate optimal axis limits for crossplot visualization based on data distribution analysis.
    Uses global percentiles as fallback when individual well data is insufficient or invalid.

    Args:
        x_data: Array of x-axis data (calculated curve: EEI/CPEI/PEIL)
        y_data: Array of y-axis data (target log)
        correlation: Correlation coefficient (optional, for scaling optimization)
        global_x_min: Global minimum for x-axis (fallback)
        global_x_max: Global maximum for x-axis (fallback)
        global_y_min: Global minimum for y-axis (fallback)
        global_y_max: Global maximum for y-axis (fallback)
        well_name: Name of the well (for logging)

    Returns:
        tuple: (x_min, x_max, y_min, y_max) optimized axis limits
    """
    try:
        # Remove NaN values for analysis
        valid_mask = ~(np.isnan(x_data) | np.isnan(y_data))
        x_clean = x_data[valid_mask]
        y_clean = y_data[valid_mask]

        if len(x_clean) < 2:
            # Use global percentiles as fallback if insufficient data
            logger.warning(f"Insufficient valid data for crossplot limits in well {well_name} "
                          f"({len(x_clean)} valid points). Using global percentiles as fallback.")

            # Use global percentiles if available, otherwise use simple range
            if (global_x_min is not None and global_x_max is not None and
                global_y_min is not None and global_y_max is not None):
                return global_x_min, global_x_max, global_y_min, global_y_max
            else:
                # Last resort: try simple range with robust NaN handling
                try:
                    x_min_fallback = np.nanmin(x_data) if np.isfinite(np.nanmin(x_data)) else 0.0
                    x_max_fallback = np.nanmax(x_data) if np.isfinite(np.nanmax(x_data)) else 1.0
                    y_min_fallback = np.nanmin(y_data) if np.isfinite(np.nanmin(y_data)) else 0.0
                    y_max_fallback = np.nanmax(y_data) if np.isfinite(np.nanmax(y_data)) else 1.0

                    # Ensure min < max
                    if x_min_fallback >= x_max_fallback:
                        x_min_fallback, x_max_fallback = 0.0, 1.0
                    if y_min_fallback >= y_max_fallback:
                        y_min_fallback, y_max_fallback = 0.0, 1.0

                    return x_min_fallback, x_max_fallback, y_min_fallback, y_max_fallback
                except:
                    logger.error(f"All fallback methods failed for well {well_name}. Using default limits (0, 1).")
                    return 0.0, 1.0, 0.0, 1.0

        # Calculate basic statistics for both axes
        x_stats = {
            'mean': np.mean(x_clean),
            'std': np.std(x_clean),
            'median': np.median(x_clean),
            'q25': np.percentile(x_clean, 25),
            'q75': np.percentile(x_clean, 75),
            'iqr': np.percentile(x_clean, 75) - np.percentile(x_clean, 25)
        }

        y_stats = {
            'mean': np.mean(y_clean),
            'std': np.std(y_clean),
            'median': np.median(y_clean),
            'q25': np.percentile(y_clean, 25),
            'q75': np.percentile(y_clean, 75),
            'iqr': np.percentile(y_clean, 75) - np.percentile(y_clean, 25)
        }

        # Detect outliers using IQR method
        x_outlier_threshold = 1.5 * x_stats['iqr']
        y_outlier_threshold = 1.5 * y_stats['iqr']

        x_lower_fence = x_stats['q25'] - x_outlier_threshold
        x_upper_fence = x_stats['q75'] + x_outlier_threshold
        y_lower_fence = y_stats['q25'] - y_outlier_threshold
        y_upper_fence = y_stats['q75'] + y_outlier_threshold

        # Calculate data density in different regions
        x_core_mask = (x_clean >= x_lower_fence) & (x_clean <= x_upper_fence)
        y_core_mask = (y_clean >= y_lower_fence) & (y_clean <= y_upper_fence)
        core_data_ratio = np.sum(x_core_mask & y_core_mask) / len(x_clean)

        # Adaptive percentile selection based on data characteristics
        if core_data_ratio > 0.85:
            # Most data is well-behaved, use tighter bounds
            x_percentiles = [5, 95]
            y_percentiles = [5, 95]
        elif core_data_ratio > 0.70:
            # Some outliers present, use moderate bounds
            x_percentiles = [2, 98]
            y_percentiles = [2, 98]
        else:
            # Many outliers, use wider bounds to capture more data
            x_percentiles = [1, 99]
            y_percentiles = [1, 99]

        # Adjust percentiles based on correlation strength
        if correlation is not None:
            abs_corr = abs(correlation)
            if abs_corr > 0.7:
                # Strong correlation: tighten bounds to focus on main trend
                x_percentiles = [max(x_percentiles[0], 3), min(x_percentiles[1], 97)]
                y_percentiles = [max(y_percentiles[0], 3), min(y_percentiles[1], 97)]
            elif abs_corr < 0.3:
                # Weak correlation: expand bounds to show scatter pattern
                x_percentiles = [max(x_percentiles[0] - 1, 0.5), min(x_percentiles[1] + 1, 99.5)]
                y_percentiles = [max(y_percentiles[0] - 1, 0.5), min(y_percentiles[1] + 1, 99.5)]

        # Calculate final limits
        x_min = np.percentile(x_clean, x_percentiles[0])
        x_max = np.percentile(x_clean, x_percentiles[1])
        y_min = np.percentile(y_clean, y_percentiles[0])
        y_max = np.percentile(y_clean, y_percentiles[1])

        # Add small buffer to prevent data points from touching axes
        x_range = x_max - x_min
        y_range = y_max - y_min

        buffer_factor = 0.02  # 2% buffer
        x_buffer = x_range * buffer_factor
        y_buffer = y_range * buffer_factor

        x_min -= x_buffer
        x_max += x_buffer
        y_min -= y_buffer
        y_max += y_buffer

        # Ensure minimum range to avoid degenerate plots
        min_range_factor = 0.01
        if x_range < min_range_factor * abs(x_stats['mean']):
            center = (x_min + x_max) / 2
            half_range = min_range_factor * abs(x_stats['mean']) / 2
            x_min = center - half_range
            x_max = center + half_range

        if y_range < min_range_factor * abs(y_stats['mean']):
            center = (y_min + y_max) / 2
            half_range = min_range_factor * abs(y_stats['mean']) / 2
            y_min = center - half_range
            y_max = center + half_range

        return x_min, x_max, y_min, y_max

    except Exception as e:
        logger.warning(f"Error in calculate_optimal_crossplot_limits for well {well_name}: {str(e)}, using fallback methods")

        # Try global percentiles first
        if (global_x_min is not None and global_x_max is not None and
            global_y_min is not None and global_y_max is not None):
            logger.info(f"Using global percentiles as fallback for crossplot limits in well {well_name}")
            return global_x_min, global_x_max, global_y_min, global_y_max

        # Try simple percentile method with robust error handling
        try:
            x_min = np.nanpercentile(x_data, 2)
            x_max = np.nanpercentile(x_data, 98)
            y_min = np.nanpercentile(y_data, 2)
            y_max = np.nanpercentile(y_data, 98)

            # Validate that percentiles are finite
            if not all(np.isfinite([x_min, x_max, y_min, y_max])):
                raise ValueError("Percentiles are not finite")

            # Ensure min < max
            if x_min >= x_max:
                x_min, x_max = 0.0, 1.0
            if y_min >= y_max:
                y_min, y_max = 0.0, 1.0

            return x_min, x_max, y_min, y_max

        except Exception as e2:
            logger.error(f"All fallback methods failed for crossplot limits in well {well_name}: {str(e2)}. Using default limits (0, 1).")
            return 0.0, 1.0, 0.0, 1.0

# calculate_global_percentiles_for_axis_limits function moved to ui/plotting_components.py
def calculate_global_percentiles_for_axis_limits_REMOVED(all_wells_data):
    """
    Calculate global 2nd and 98th percentiles from all wells' target and normalized_eei data.

    This function combines data from all wells to provide robust axis scaling that works
    consistently across all wells, even when individual wells have problematic data.

    Args:
        all_wells_data: List of dictionaries containing well data with keys:
                       'target', 'normalized_eei', 'well_name', etc.

    Returns:
        tuple: (global_min, global_max) - 2nd and 98th percentiles from combined data
               Returns (0, 1) as fallback if no valid data is found
    """
    try:
        all_valid_data = []
        wells_with_data = 0

        # Collect all valid data from all wells
        for well_data in all_wells_data:
            target = well_data.get('target')
            normalized_eei = well_data.get('normalized_eei')
            well_name = well_data.get('well_name', 'Unknown')

            # Skip wells with missing data
            if target is None or normalized_eei is None:
                logger.debug(f"Skipping well {well_name} - missing target or normalized_eei data")
                continue

            # Convert to numpy arrays if needed
            target = np.asarray(target)
            normalized_eei = np.asarray(normalized_eei)

            # Create finite value mask for both arrays
            target_finite_mask = np.isfinite(target)
            eei_finite_mask = np.isfinite(normalized_eei)

            # Extract finite values
            target_finite = target[target_finite_mask]
            eei_finite = normalized_eei[eei_finite_mask]

            # Add to global collection if we have valid data
            if len(target_finite) > 0:
                all_valid_data.extend(target_finite)
                wells_with_data += 1
                logger.debug(f"Added {len(target_finite)} target values from well {well_name}")

            if len(eei_finite) > 0:
                all_valid_data.extend(eei_finite)
                logger.debug(f"Added {len(eei_finite)} normalized_eei values from well {well_name}")

        # Check if we have sufficient data
        if len(all_valid_data) < 2:
            logger.warning(f"Insufficient valid data for global percentile calculation. "
                          f"Found {len(all_valid_data)} valid values from {wells_with_data} wells. "
                          f"Using fallback limits (0, 1).")
            return 0.0, 1.0

        # Convert to numpy array for percentile calculation
        all_valid_data = np.array(all_valid_data)

        # Calculate global percentiles using nanpercentile for extra safety
        global_min = np.nanpercentile(all_valid_data, 2)
        global_max = np.nanpercentile(all_valid_data, 98)

        # Validate that percentiles are finite
        if not (np.isfinite(global_min) and np.isfinite(global_max)):
            logger.warning(f"Global percentiles are not finite: min={global_min}, max={global_max}. "
                          f"Using fallback limits (0, 1).")
            return 0.0, 1.0

        # Ensure min < max
        if global_min >= global_max:
            logger.warning(f"Global min ({global_min}) >= max ({global_max}). "
                          f"Adding small buffer to max value.")
            global_max = global_min + 1.0

        logger.info(f"Calculated global percentiles from {len(all_valid_data)} values "
                   f"across {wells_with_data} wells: min={global_min:.4f}, max={global_max:.4f}")

        return global_min, global_max

    except Exception as e:
        logger.error(f"Error calculating global percentiles: {str(e)}. Using fallback limits (0, 1).")
        return 0.0, 1.0

# plot_eei_vs_target function moved to ui/plotting_components.py
def plot_eei_vs_target_REMOVED(all_wells_data, target_log, depth_ranges):
    """
    Create separate three-column plots for each well:
    1. Depth vs VOL_WETCLAY (if available)
    2. Depth vs Target Log and calculated curve (EEI/CPEI/PEIL with optimum parameters)
    3. Crossplot of calculated curve vs Target Log

    Automatically detects analysis type from the angle parameter format.
    Uses global percentile statistics for robust axis scaling across all wells.
    """
    # Calculate global percentiles from all wells for robust axis scaling
    global_min, global_max = calculate_global_percentiles_for_axis_limits(all_wells_data)
    logger.info(f"Using global percentiles for axis scaling: min={global_min:.4f}, max={global_max:.4f}")

    # Ask user for x-axis range for target log and EEI in the second column
    root = tk.Tk()
    root.withdraw()

    # Create a custom dialog that allows empty inputs
    class CustomRangeDialog(tk.simpledialog.Dialog):
        def __init__(self, parent, title, target_log):
            self.target_log = target_log
            self.x_min = None
            self.x_max = None
            super().__init__(parent, title)

        def body(self, master):
            ttk.Label(master, text=f"Enter x-axis range for {self.target_log} and EEI:").grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
            ttk.Label(master, text="Minimum value (leave empty for auto):").grid(row=1, column=0, sticky="w", pady=5)
            ttk.Label(master, text="Maximum value (leave empty for auto):").grid(row=2, column=0, sticky="w", pady=5)

            self.min_entry = ttk.Entry(master)
            self.min_entry.grid(row=1, column=1, padx=5, pady=5)

            self.max_entry = ttk.Entry(master)
            self.max_entry.grid(row=2, column=1, padx=5, pady=5)

            return self.min_entry  # Initial focus

        def validate(self):
            min_val = self.min_entry.get().strip()
            max_val = self.max_entry.get().strip()

            try:
                if min_val:
                    self.x_min = float(min_val)
                if max_val:
                    self.x_max = float(max_val)

                # Ensure min is less than max if both are provided
                if self.x_min is not None and self.x_max is not None and self.x_min >= self.x_max:
                    messagebox.showerror("Invalid Range", "Minimum value must be less than maximum value.")
                    return False

                return True
            except ValueError:
                messagebox.showerror("Invalid Input", "Please enter valid numbers or leave fields empty.")
                return False

        def apply(self):
            # Values are already stored in self.x_min and self.x_max
            pass

    # Show the custom dialog
    dialog = CustomRangeDialog(root, "X-Axis Range", target_log)
    x_min = dialog.x_min
    x_max = dialog.x_max

    for well_data in all_wells_data:
        well_name = well_data['well_name']
        depth = well_data['depth']
        target = well_data['target']
        normalized_eei = well_data['normalized_eei']
        angle = well_data['angle']
        vol_wetclay = well_data.get('vol_wetclay')

        # Check if we have all necessary data
        if depth is None or target is None or normalized_eei is None:
            print(f"Skipping well {well_name} due to missing data.")
            continue

        # Detect analysis type from angle parameter format
        if isinstance(angle, str) and 'n=' in angle and 'phi=' in angle:
            if 'CPEI' in str(angle).upper():
                analysis_type = 'CPEI'
            elif 'PEIL' in str(angle).upper():
                analysis_type = 'PEIL'
            else:
                # Default to CPEI for n,phi format without explicit type
                analysis_type = 'CPEI' if 'n=' in angle else 'EEI'
        else:
            analysis_type = 'EEI'

        # Calculate correlation, automatically omitting NaN values
        correlation = nanaware_corrcoef(normalized_eei, target)

        # Get the analysis depth range and add buffer
        top_depth, bottom_depth = depth_ranges[well_name]
        y_min, y_max = top_depth - 30, bottom_depth + 30

        # Determine the number of subplots based on available data
        if vol_wetclay is not None:
            fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 10))
        else:
            fig, (ax2, ax3) = plt.subplots(1, 2, figsize=(15, 10))
            print(f"Warning: VOL_WETCLAY not available for well {well_name}, plotting only 2 columns.")

        fig.suptitle(f'{well_name}: {analysis_type} vs {target_log} Analysis', fontsize=16)

        # First column: Depth vs VOL_WETCLAY (if available)
        if vol_wetclay is not None:
            valid_vcl_mask = ~np.isnan(vol_wetclay)
            ax1.plot(vol_wetclay[valid_vcl_mask], depth[valid_vcl_mask], label='VOL_WETCLAY', color='green')
            ax1.set_xlabel('VOL_WETCLAY')
            ax1.set_ylabel('Depth')
            ax1.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards
            ax1.set_xlim(0, 1)  # Set x-axis limits for VOL_WETCLAY
            ax1.axhline(y=top_depth, color='r', linestyle='--', label='Top')
            ax1.axhline(y=bottom_depth, color='b', linestyle='--', label='Bottom')
            ax1.legend()

        # Second column: Depth vs Target Log and Normalized EEI
        valid_mask = ~np.isnan(target) & ~np.isnan(normalized_eei) & ~np.isnan(depth)
        ax2.plot(np.ma.masked_array(target, ~valid_mask),
                 np.ma.masked_array(depth, ~valid_mask),
                 label=target_log, color='blue')
        # Format angle display based on analysis type
        if analysis_type == 'EEI':
            angle_display = f'{safe_format_float(angle, precision=1, default="N/A")}°'
        else:
            angle_display = str(angle) if angle is not None else "N/A"

        ax2.plot(np.ma.masked_array(normalized_eei, ~valid_mask),
                 np.ma.masked_array(depth, ~valid_mask),
                 label=f'{analysis_type} ({angle_display})', color='red')
        ax2.set_xlabel('Value')
        ax2.set_ylabel('Depth')
        ax2.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards

        # Set x-axis limits based on user input or global percentiles
        try:
            if x_min is not None and x_max is not None:
                # Both min and max provided by user
                final_x_min, final_x_max = x_min, x_max
            elif x_min is not None:
                # Only min provided, use global max as fallback
                final_x_min, final_x_max = x_min, global_max
            elif x_max is not None:
                # Only max provided, use global min as fallback
                final_x_min, final_x_max = global_min, x_max
            else:
                # No user input, use global percentiles for consistent scaling across all wells
                final_x_min, final_x_max = global_min, global_max

            # Validate that final limits are finite
            if not (np.isfinite(final_x_min) and np.isfinite(final_x_max)):
                logger.warning(f"Final axis limits are not finite for well {well_name}: "
                              f"min={final_x_min}, max={final_x_max}. Using fallback limits (0, 1).")
                final_x_min, final_x_max = 0.0, 1.0

            # Ensure min < max
            if final_x_min >= final_x_max:
                logger.warning(f"Final x_min ({final_x_min}) >= x_max ({final_x_max}) for well {well_name}. "
                              f"Adding small buffer to max value.")
                final_x_max = final_x_min + 1.0

            ax2.set_xlim(final_x_min, final_x_max)
            logger.debug(f"Set axis limits for well {well_name}: x_min={final_x_min:.4f}, x_max={final_x_max:.4f}")

        except Exception as e:
            logger.error(f"Error setting axis limits for well {well_name}: {str(e)}. Using fallback limits (0, 1).")
            ax2.set_xlim(0.0, 1.0)
        ax2.axhline(y=top_depth, color='r', linestyle='--', label='Top')
        ax2.axhline(y=bottom_depth, color='b', linestyle='--', label='Bottom')
        ax2.legend()

        # Third column: Scatter plot of Normalized calculated curve vs Target Log
        ax3.scatter(np.ma.masked_array(normalized_eei, ~valid_mask),
                    np.ma.masked_array(target, ~valid_mask),
                    alpha=0.5)
        ax3.set_xlabel(f'{analysis_type} ({angle_display})')
        ax3.set_ylabel(target_log)

        # Calculate optimal axis limits using advanced scaling algorithm with global percentiles fallback
        try:
            x_min, x_max, y_min, y_max = calculate_optimal_crossplot_limits(
                normalized_eei[valid_mask],
                target[valid_mask],
                correlation,
                global_x_min=global_min,
                global_x_max=global_max,
                global_y_min=global_min,  # Use same global percentiles for y-axis
                global_y_max=global_max,
                well_name=well_name
            )

            # Validate that final limits are finite before setting
            if not all(np.isfinite([x_min, x_max, y_min, y_max])):
                logger.warning(f"Crossplot limits are not finite for well {well_name}: "
                              f"x_min={x_min}, x_max={x_max}, y_min={y_min}, y_max={y_max}. "
                              f"Using global percentiles as fallback.")
                x_min, x_max, y_min, y_max = global_min, global_max, global_min, global_max

            ax3.set_xlim(x_min, x_max)
            ax3.set_ylim(y_min, y_max)
            logger.debug(f"Set crossplot limits for well {well_name}: "
                        f"x_min={x_min:.4f}, x_max={x_max:.4f}, y_min={y_min:.4f}, y_max={y_max:.4f}")

        except Exception as e:
            logger.error(f"Error setting crossplot limits for well {well_name}: {str(e)}. Using global percentiles.")
            ax3.set_xlim(global_min, global_max)
            ax3.set_ylim(global_min, global_max)

        # Calculate linear regression
        x_for_regression = normalized_eei[valid_mask]
        y_for_regression = target[valid_mask]
        if len(x_for_regression) > 1:  # Need at least 2 points for regression
            # Ensure no NaN values in the data
            reg_mask = ~np.isnan(x_for_regression) & ~np.isnan(y_for_regression)
            if np.sum(reg_mask) > 1:  # Still need at least 2 valid points after removing NaNs
                regression = stats.linregress(x_for_regression[reg_mask], y_for_regression[reg_mask])
                # Plot regression line using optimized axis limits
                x_line = np.array([x_min, x_max])
                y_line = regression.slope * x_line + regression.intercept
                ax3.plot(x_line, y_line, 'r-', linewidth=2, label='Linear regression')

                # Add regression equation and R² to the plot
                equation = f'y = {regression.slope:.4f}x + {regression.intercept:.4f}'
                r_squared = regression.rvalue**2
                ax3.text(0.05, 0.85, equation, transform=ax3.transAxes, verticalalignment='top')
                ax3.text(0.05, 0.75, f'R² = {r_squared:.4f}', transform=ax3.transAxes, verticalalignment='top')

        # Add a diagonal line (y = x) for reference using the appropriate range
        diag_min = max(x_min, y_min)
        diag_max = min(x_max, y_max)
        if diag_min < diag_max:  # Only draw if there's a valid range
            ax3.plot([diag_min, diag_max], [diag_min, diag_max], 'k--', alpha=0.5, label='y = x')

        # Add correlation value to the plot
        ax3.text(0.05, 0.95, f'Correlation: {safe_format_float(correlation, precision=4, default="N/A")}',
                 transform=ax3.transAxes, verticalalignment='top')

        # Add legend
        ax3.legend(loc='lower right')

        # Add correlation value to the plot title
        fig.suptitle(f'{well_name}: {analysis_type} vs {target_log} Analysis\nCorrelation: {safe_format_float(correlation, precision=4, default="N/A")}', fontsize=16)

        plt.tight_layout()
        plt.show()

def individual_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name, depth_ranges, analysis_method, calcmethod, k_method, k_value, alternative_mnemonics=None):
    """
    Legacy wrapper for individual well analysis - delegates to workflow orchestration module.

    This function has been moved to ui.workflow_orchestration for better modularization.
    This wrapper maintains backward compatibility.

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        target_log_generic_name: Generic name of the target log
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        analysis_method: 1=EEI, 2=CPEI, 3=PEIL
        calcmethod: Method to use for EEI calculation (only used for EEI)
        k_method: Method for k value (only used for EEI)
        k_value: Constant k value (only used for EEI)
        alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

    Returns:
        Tuple of (all_wells_results, all_wells_data)
    """
    return _individual_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name,
                                   depth_ranges, analysis_method, calcmethod, k_method, k_value,
                                   alternative_mnemonics)

def merged_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name, depth_ranges, analysis_method, calcmethod, k_method, k_value, alternative_mnemonics=None):
    """
    Legacy wrapper for merged well analysis - delegates to workflow orchestration module.

    This function has been moved to ui.workflow_orchestration for better modularization.
    This wrapper maintains backward compatibility.

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        target_log_generic_name: Generic name of the target log
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        analysis_method: 1=EEI, 2=CPEI, 3=PEIL
        calcmethod: Method to use for EEI calculation (only used for EEI)
        k_method: Method for k value (only used for EEI)
        k_value: Constant k value (only used for EEI)
        alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

    Returns:
        List of well data for plotting
    """
    return _merged_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name,
                                depth_ranges, analysis_method, calcmethod, k_method, k_value,
                                alternative_mnemonics)

# show_next_action_dialog function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def show_next_action_dialog():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.show_next_action_dialog()

def load_boundaries_from_excel(title="Select Excel file with boundary information"):
    """
    Load boundary information from an Excel file.

    Args:
        title: The title to display in the file dialog

    Returns:
        DataFrame containing well names, surface names, and measured depths
        or None if no file was selected or an error occurred.
    """
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")]
    )

    if not file_path:
        print("No Excel file selected.")
        return None

    try:
        # Load the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            messagebox.showerror(
                "Missing Columns",
                f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                f"Please ensure the file contains columns named: {', '.join(required_columns)}"
            )
            return None

        # Basic validation
        if df.empty:
            messagebox.showerror("Empty File", "The Excel file contains no data.")
            return None

        print(f"Successfully loaded boundary data from {file_path}")
        print(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        messagebox.showerror("Error Loading File", f"An error occurred while loading the Excel file:\n{str(e)}")
        print(f"Error loading Excel file: {str(e)}")
        return None

def filter_excel_data_for_las_wells(df, las_files):
    """
    Filter Excel data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths
        las_files: List of LAS file objects

    Returns:
        Filtered DataFrame containing only boundaries for wells in the LAS files
    """
    if df is None:
        return None

    # Get the list of well names from the LAS files
    las_well_names = [las.well.WELL.value for las in las_files]

    # Filter the DataFrame to only include rows where the Well column value is in las_well_names
    filtered_df = df[df['Well'].isin(las_well_names)]

    # Check if we have any matching wells
    if filtered_df.empty:
        print("Warning: No matching wells found in Excel file. The Excel file contains wells:",
              ", ".join(df['Well'].unique()))
        print("But the loaded LAS files contain wells:", ", ".join(las_well_names))
        return None

    # Log the filtering results
    original_well_count = df['Well'].nunique()
    filtered_well_count = filtered_df['Well'].nunique()
    print(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    print(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df

def load_excel_depth_ranges(las_files):
    """
    Prompt the user to load an Excel file with depth ranges at the beginning of the program.
    Filter the data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        las_files: List of LAS file objects

    Returns:
        DataFrame containing well names, surface names, and measured depths (filtered for LAS wells)
        or None if no file was selected, the user canceled, or an error occurred.
    """
    # Ask user if they want to load an Excel file with depth ranges
    root = tk.Tk()
    root.withdraw()

    load_excel = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'."
    )

    if not load_excel:
        print("User chose not to load Excel file with depth ranges at this time.")
        return None

    # Load the Excel file
    df = load_boundaries_from_excel("Select Excel file with depth ranges")

    # Filter the data to only include boundaries for wells in the LAS files
    return filter_excel_data_for_las_wells(df, las_files)

# select_boundaries_for_all_wells function moved to ui/dialog_systems.py as a helper method

# Legacy function wrapper for backward compatibility
def select_boundaries_for_all_wells(df, las_well_names):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems._select_boundaries_for_all_wells(df, las_well_names)

# select_boundaries_from_excel function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def select_boundaries_from_excel(df, well_name):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.select_boundaries_from_excel(df, well_name)

# get_depth_ranges function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df=None):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df)

# get_target_log function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used=False):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used)


# select_alternative_mnemonic function moved to ui/dialog_systems.py

# Legacy function wrapper for backward compatibility
def select_alternative_mnemonic(las, missing_target_log, well_name):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.select_alternative_mnemonic(las, missing_target_log, well_name)


def run_eei_analysis():
    """
    Legacy wrapper for the complete EEI analysis workflow - delegates to workflow orchestration module.

    This function has been moved to ui.workflow_orchestration for better modularization.
    This wrapper maintains backward compatibility.

    Returns:
        bool: True if user wants to restart, False if user wants to exit
    """
    return _run_eei_analysis()


# MAIN EXECUTION SCRIPT
if __name__ == "__main__":
    print("="*80)
    print("🎯 EEI ANALYSIS TOOL")
    print("="*80)
    print("Welcome to the Extended Elastic Impedance (EEI) Analysis Tool!")
    print("This tool performs correlation analysis between EEI and target logs.\n")

    # Main execution loop
    while True:
        try:
            # Run the analysis
            should_restart = run_eei_analysis()

            # Check if user wants to restart or exit
            if not should_restart:
                break

        except KeyboardInterrupt:
            print("\n\nProgram interrupted by user. Exiting...")
            break
        except Exception as e:
            print(f"\n❌ An unexpected error occurred: {str(e)}")
            print("The program will exit. Please check your data and try again.")
            break

    print("\nProgram terminated.")
    print("="*80)