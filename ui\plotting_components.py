"""
Plotting Components Module for EEI Cross-Correlation Analysis

This module provides comprehensive plotting and visualization functionality for EEI, CPEI, and PEIL
cross-correlation analysis. It handles all matplotlib-based plotting operations including:
- Main multi-panel visualization (plot_eei_vs_target)
- Axis scaling and optimization
- Cross-plot generation
- Correlation plots and heatmaps
- Statistical analysis and distribution plots

Created as part of Phase 3A modularization to extract plotting functionality from the main file.
"""

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
import tkinter.ttk as ttk
import tkinter.simpledialog
import tkinter.messagebox as messagebox
from scipy import stats
import logging

# Import from existing modules
from ui.helper_functions import safe_format_float, safe_format_parameter_string
from eei_calculation_engine import nanaware_corrcoef

# Import statistical analysis components
try:
    from ui.statistical_analysis import get_statistical_analyzer
    STATISTICAL_ANALYSIS_AVAILABLE = True
except ImportError:
    STATISTICAL_ANALYSIS_AVAILABLE = False
    logging.warning("Statistical analysis module not available")

# Set up logging
logger = logging.getLogger(__name__)


class PlottingComponents:
    """
    Comprehensive plotting system for EEI cross-correlation analysis.

    This class manages all plotting and visualization aspects including:
    - Multi-panel cross-plots
    - Axis scaling and optimization
    - Matplotlib figure management
    - Plot customization and styling
    """

    def __init__(self):
        """Initialize the plotting components with default settings."""
        # Plot configuration state
        self.plot_settings = {
            'figure_size_main': (20, 10),  # For 3-column plots
            'figure_size_two_col': (15, 10),  # For 2-column plots
            'figure_size_correlation': (12, 6),  # For correlation vs angle plots
            'figure_size_heatmap': (12, 8),  # For correlation heatmaps
            'dpi': 100,
            'color_scheme': 'default'
        }

        # Axis limits and scaling
        self.axis_limits = {}

        # Data caching for performance
        self.plot_data_cache = {}

        # Matplotlib figure management
        self.active_figures = {}

        # Color schemes
        self.color_schemes = {
            'default': {
                'target_log': 'blue',
                'eei_curve': 'red',
                'vcl_curve': 'green',
                'top_line': 'red',
                'bottom_line': 'blue',
                'regression_line': 'red',
                'diagonal_line': 'black'
            }
        }

        logger.info("PlottingComponents initialized successfully")

    def calculate_global_percentiles_for_axis_limits(self, all_wells_data):
        """
        Calculate global 2nd and 98th percentiles from all wells' target and normalized_eei data.

        This function combines data from all wells to provide robust axis scaling that works
        consistently across all wells, even when individual wells have problematic data.

        Args:
            all_wells_data: List of dictionaries containing well data with keys:
                           'target', 'normalized_eei', 'well_name', etc.

        Returns:
            tuple: (global_min, global_max) - 2nd and 98th percentiles from combined data
                   Returns (0, 1) as fallback if no valid data is found
        """
        try:
            all_valid_data = []
            wells_with_data = 0

            for well_data in all_wells_data:
                well_name = well_data.get('well_name', 'Unknown')
                target = well_data.get('target')
                normalized_eei = well_data.get('normalized_eei')

                if target is None or normalized_eei is None:
                    logger.debug(f"Skipping well {well_name} for global percentiles: missing target or normalized_eei data")
                    continue

                # Combine target and normalized_eei data for this well
                try:
                    target_array = np.array(target)
                    eei_array = np.array(normalized_eei)

                    # Remove NaN values
                    valid_target = target_array[~np.isnan(target_array)]
                    valid_eei = eei_array[~np.isnan(eei_array)]

                    if len(valid_target) > 0:
                        all_valid_data.extend(valid_target)
                    if len(valid_eei) > 0:
                        all_valid_data.extend(valid_eei)

                    if len(valid_target) > 0 or len(valid_eei) > 0:
                        wells_with_data += 1
                        logger.debug(f"Added {len(valid_target)} target and {len(valid_eei)} EEI values from well {well_name}")

                except Exception as e:
                    logger.warning(f"Error processing data from well {well_name} for global percentiles: {str(e)}")
                    continue

            # Check if we have sufficient data
            if len(all_valid_data) < 2:
                logger.warning(f"Insufficient valid data for global percentile calculation. "
                              f"Found {len(all_valid_data)} valid values from {wells_with_data} wells. "
                              f"Using fallback limits (0, 1).")
                return 0.0, 1.0

            # Convert to numpy array for percentile calculation
            all_valid_data = np.array(all_valid_data)

            # Calculate global percentiles using nanpercentile for extra safety
            global_min = np.nanpercentile(all_valid_data, 2)
            global_max = np.nanpercentile(all_valid_data, 98)

            # Validate that percentiles are finite
            if not (np.isfinite(global_min) and np.isfinite(global_max)):
                logger.warning(f"Global percentiles are not finite: min={global_min}, max={global_max}. "
                              f"Using fallback limits (0, 1).")
                return 0.0, 1.0

            # Ensure min < max
            if global_min >= global_max:
                logger.warning(f"Global min ({global_min}) >= max ({global_max}). "
                              f"Adding small buffer to max value.")
                global_max = global_min + 1.0

            logger.info(f"Calculated global percentiles from {len(all_valid_data)} values "
                       f"across {wells_with_data} wells: min={global_min:.4f}, max={global_max:.4f}")

            return global_min, global_max

        except Exception as e:
            logger.error(f"Error calculating global percentiles: {str(e)}. Using fallback limits (0, 1).")
            return 0.0, 1.0

    def calculate_optimal_crossplot_limits(self, x_data, y_data, correlation=None,
                                         global_x_min=None, global_x_max=None,
                                         global_y_min=None, global_y_max=None,
                                         well_name="Unknown"):
        """
        Calculate optimal axis limits for crossplot visualization based on data distribution analysis.
        Uses global percentiles as fallback when individual well data is insufficient or invalid.

        Args:
            x_data: Array of x-axis data (calculated curve: EEI/CPEI/PEIL)
            y_data: Array of y-axis data (target log)
            correlation: Correlation coefficient (optional, for scaling optimization)
            global_x_min: Global minimum for x-axis (fallback)
            global_x_max: Global maximum for x-axis (fallback)
            global_y_min: Global minimum for y-axis (fallback)
            global_y_max: Global maximum for y-axis (fallback)
            well_name: Name of the well (for logging)

        Returns:
            tuple: (x_min, x_max, y_min, y_max) optimized axis limits
        """
        try:
            # Remove NaN values for analysis
            valid_mask = ~(np.isnan(x_data) | np.isnan(y_data))
            x_clean = x_data[valid_mask]
            y_clean = y_data[valid_mask]

            # Check if we have sufficient valid data
            if len(x_clean) < 10:  # Need at least 10 points for meaningful statistics
                logger.warning(f"Insufficient valid data points ({len(x_clean)}) for crossplot optimization in well {well_name}. Using global percentiles.")
                if (global_x_min is not None and global_x_max is not None and
                    global_y_min is not None and global_y_max is not None):
                    return global_x_min, global_x_max, global_y_min, global_y_max
                else:
                    return 0.0, 1.0, 0.0, 1.0

            # Calculate percentiles for robust outlier handling
            x_p2, x_p98 = np.percentile(x_clean, [2, 98])
            y_p2, y_p98 = np.percentile(y_clean, [2, 98])

            # Validate percentiles
            if not all(np.isfinite([x_p2, x_p98, y_p2, y_p98])):
                logger.warning(f"Invalid percentiles calculated for well {well_name}. Using global percentiles.")
                if (global_x_min is not None and global_x_max is not None and
                    global_y_min is not None and global_y_max is not None):
                    return global_x_min, global_x_max, global_y_min, global_y_max
                else:
                    return 0.0, 1.0, 0.0, 1.0

            # Ensure min < max for both axes
            if x_p2 >= x_p98:
                x_p2, x_p98 = x_p2 - 0.1, x_p2 + 0.1
            if y_p2 >= y_p98:
                y_p2, y_p98 = y_p2 - 0.1, y_p2 + 0.1

            # Add small buffer for better visualization (5% of range)
            x_range = x_p98 - x_p2
            y_range = y_p98 - y_p2

            x_buffer = max(0.05 * x_range, 0.01)  # At least 0.01 buffer
            y_buffer = max(0.05 * y_range, 0.01)  # At least 0.01 buffer

            x_min = x_p2 - x_buffer
            x_max = x_p98 + x_buffer
            y_min = y_p2 - y_buffer
            y_max = y_p98 + y_buffer

            # Correlation-based optimization (if correlation is strong, tighten limits)
            if correlation is not None and abs(correlation) > 0.7:
                # For strong correlations, use slightly tighter limits
                tightening_factor = 0.9
                x_center = (x_min + x_max) / 2
                y_center = (y_min + y_max) / 2
                x_half_range = (x_max - x_min) / 2 * tightening_factor
                y_half_range = (y_max - y_min) / 2 * tightening_factor

                x_min = x_center - x_half_range
                x_max = x_center + x_half_range
                y_min = y_center - y_half_range
                y_max = y_center + y_half_range

            logger.debug(f"Calculated optimal crossplot limits for well {well_name}: "
                        f"x_min={x_min:.4f}, x_max={x_max:.4f}, y_min={y_min:.4f}, y_max={y_max:.4f}")

            return x_min, x_max, y_min, y_max

        except Exception as e:
            logger.warning(f"Error in calculate_optimal_crossplot_limits for well {well_name}: {str(e)}, using fallback methods")

            # Try global percentiles first
            if (global_x_min is not None and global_x_max is not None and
                global_y_min is not None and global_y_max is not None):
                logger.info(f"Using global percentiles as fallback for crossplot limits in well {well_name}")
                return global_x_min, global_x_max, global_y_min, global_y_max

            # Try simple percentile method with robust error handling
            try:
                x_min = np.nanpercentile(x_data, 2)
                x_max = np.nanpercentile(x_data, 98)
                y_min = np.nanpercentile(y_data, 2)
                y_max = np.nanpercentile(y_data, 98)

                # Validate that percentiles are finite
                if not all(np.isfinite([x_min, x_max, y_min, y_max])):
                    raise ValueError("Percentiles are not finite")

                # Ensure min < max
                if x_min >= x_max:
                    x_min, x_max = 0.0, 1.0
                if y_min >= y_max:
                    y_min, y_max = 0.0, 1.0

                return x_min, x_max, y_min, y_max

            except Exception as e2:
                logger.error(f"All fallback methods failed for crossplot limits in well {well_name}: {str(e2)}. Using default limits (0, 1).")
                return 0.0, 1.0, 0.0, 1.0

    def _create_custom_range_dialog(self, parent, target_log):
        """
        Create a custom dialog for x-axis range input.

        Args:
            parent: Parent tkinter window
            target_log: Name of the target log for display

        Returns:
            tuple: (x_min, x_max) or (None, None) if cancelled
        """
        class CustomRangeDialog(tk.simpledialog.Dialog):
            def __init__(self, parent, title, target_log):
                self.target_log = target_log
                self.x_min = None
                self.x_max = None
                super().__init__(parent, title)

            def body(self, master):
                ttk.Label(master, text=f"Enter x-axis range for {self.target_log} and EEI:").grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
                ttk.Label(master, text="Minimum value (leave empty for auto):").grid(row=1, column=0, sticky="w", pady=5)
                ttk.Label(master, text="Maximum value (leave empty for auto):").grid(row=2, column=0, sticky="w", pady=5)

                self.min_entry = ttk.Entry(master)
                self.min_entry.grid(row=1, column=1, padx=5, pady=5)

                self.max_entry = ttk.Entry(master)
                self.max_entry.grid(row=2, column=1, padx=5, pady=5)

                return self.min_entry  # Initial focus

            def validate(self):
                min_val = self.min_entry.get().strip()
                max_val = self.max_entry.get().strip()

                try:
                    if min_val:
                        self.x_min = float(min_val)
                    if max_val:
                        self.x_max = float(max_val)

                    # Ensure min is less than max if both are provided
                    if self.x_min is not None and self.x_max is not None and self.x_min >= self.x_max:
                        messagebox.showerror("Invalid Range", "Minimum value must be less than maximum value.")
                        return False

                    return True
                except ValueError:
                    messagebox.showerror("Invalid Input", "Please enter valid numbers or leave fields empty.")
                    return False

            def apply(self):
                # Values are already stored in self.x_min and self.x_max
                pass

        dialog = CustomRangeDialog(parent, "X-Axis Range", target_log)
        return dialog.x_min, dialog.x_max

    def plot_eei_vs_target(self, all_wells_data, target_log, depth_ranges):
        """
        Create separate three-column plots for each well:
        1. Depth vs VOL_WETCLAY (if available)
        2. Depth vs Target Log and calculated curve (EEI/CPEI/PEIL with optimum parameters)
        3. Crossplot of calculated curve vs Target Log

        Automatically detects analysis type from the angle parameter format.
        Uses global percentile statistics for robust axis scaling across all wells.

        Args:
            all_wells_data: List of dictionaries containing well data
            target_log: Name of the target log
            depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        """
        # Calculate global percentiles from all wells for robust axis scaling
        global_min, global_max = self.calculate_global_percentiles_for_axis_limits(all_wells_data)
        logger.info(f"Using global percentiles for axis scaling: min={global_min:.4f}, max={global_max:.4f}")

        # Ask user for x-axis range for target log and EEI in the second column
        root = tk.Tk()
        root.withdraw()

        # Show the custom dialog
        x_min, x_max = self._create_custom_range_dialog(root, target_log)

        for well_data in all_wells_data:
            well_name = well_data['well_name']
            depth = well_data['depth']
            target = well_data['target']
            normalized_eei = well_data['normalized_eei']
            angle = well_data['angle']
            vol_wetclay = well_data.get('vol_wetclay')

            # Check if we have all necessary data
            if depth is None or target is None or normalized_eei is None:
                print(f"Skipping well {well_name} due to missing data.")
                continue

            # Detect analysis type from angle parameter format
            if isinstance(angle, str) and 'n=' in angle and 'phi=' in angle:
                if 'CPEI' in str(angle).upper():
                    analysis_type = 'CPEI'
                elif 'PEIL' in str(angle).upper():
                    analysis_type = 'PEIL'
                else:
                    # Default to CPEI for n,phi format without explicit type
                    analysis_type = 'CPEI' if 'n=' in angle else 'EEI'
            else:
                analysis_type = 'EEI'

            # Calculate correlation, automatically omitting NaN values
            correlation = nanaware_corrcoef(normalized_eei, target)

            # Get the analysis depth range and add buffer
            top_depth, bottom_depth = depth_ranges[well_name]
            y_min, y_max = top_depth - 30, bottom_depth + 30

            # Determine the number of subplots based on available data
            if vol_wetclay is not None:
                fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=self.plot_settings['figure_size_main'])
            else:
                fig, (ax2, ax3) = plt.subplots(1, 2, figsize=self.plot_settings['figure_size_two_col'])
                print(f"Warning: VOL_WETCLAY not available for well {well_name}, plotting only 2 columns.")

            fig.suptitle(f'{well_name}: {analysis_type} vs {target_log} Analysis', fontsize=16)

            # First column: Depth vs VOL_WETCLAY (if available)
            if vol_wetclay is not None:
                valid_vcl_mask = ~np.isnan(vol_wetclay)
                ax1.plot(vol_wetclay[valid_vcl_mask], depth[valid_vcl_mask],
                        label='VOL_WETCLAY', color=self.color_schemes['default']['vcl_curve'])
                ax1.set_xlabel('VOL_WETCLAY')
                ax1.set_ylabel('Depth')
                ax1.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards
                ax1.set_xlim(0, 1)  # Set x-axis limits for VOL_WETCLAY
                ax1.axhline(y=top_depth, color=self.color_schemes['default']['top_line'],
                           linestyle='--', label='Top')
                ax1.axhline(y=bottom_depth, color=self.color_schemes['default']['bottom_line'],
                           linestyle='--', label='Bottom')
                ax1.legend()

            # Second column: Depth vs Target Log and Normalized EEI
            valid_mask = ~np.isnan(target) & ~np.isnan(normalized_eei) & ~np.isnan(depth)
            ax2.plot(np.ma.masked_array(target, ~valid_mask),
                     np.ma.masked_array(depth, ~valid_mask),
                     label=target_log, color=self.color_schemes['default']['target_log'])

            # Format angle display based on analysis type
            if analysis_type == 'EEI':
                angle_display = f'{safe_format_float(angle, precision=1, default="N/A")}°'
            else:
                angle_display = str(angle) if angle is not None else "N/A"

            ax2.plot(np.ma.masked_array(normalized_eei, ~valid_mask),
                     np.ma.masked_array(depth, ~valid_mask),
                     label=f'{analysis_type} ({angle_display})',
                     color=self.color_schemes['default']['eei_curve'])
            ax2.set_xlabel('Value')
            ax2.set_ylabel('Depth')
            ax2.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards

            # Set x-axis limits based on user input or global percentiles
            try:
                if x_min is not None and x_max is not None:
                    # Both min and max provided by user
                    final_x_min, final_x_max = x_min, x_max
                elif x_min is not None:
                    # Only min provided, use global max as fallback
                    final_x_min, final_x_max = x_min, global_max
                elif x_max is not None:
                    # Only max provided, use global min as fallback
                    final_x_min, final_x_max = global_min, x_max
                else:
                    # No user input, use global percentiles for consistent scaling across all wells
                    final_x_min, final_x_max = global_min, global_max

                # Validate that final limits are finite
                if not (np.isfinite(final_x_min) and np.isfinite(final_x_max)):
                    logger.warning(f"Final axis limits are not finite for well {well_name}: "
                                  f"min={final_x_min}, max={final_x_max}. Using fallback limits (0, 1).")
                    final_x_min, final_x_max = 0.0, 1.0

                # Ensure min < max
                if final_x_min >= final_x_max:
                    logger.warning(f"Final x_min ({final_x_min}) >= x_max ({final_x_max}) for well {well_name}. "
                                  f"Adding small buffer to max value.")
                    final_x_max = final_x_min + 1.0

                ax2.set_xlim(final_x_min, final_x_max)
                logger.debug(f"Set axis limits for well {well_name}: x_min={final_x_min:.4f}, x_max={final_x_max:.4f}")

            except Exception as e:
                logger.error(f"Error setting axis limits for well {well_name}: {str(e)}. Using fallback limits (0, 1).")
                ax2.set_xlim(0.0, 1.0)

            ax2.axhline(y=top_depth, color=self.color_schemes['default']['top_line'],
                       linestyle='--', label='Top')
            ax2.axhline(y=bottom_depth, color=self.color_schemes['default']['bottom_line'],
                       linestyle='--', label='Bottom')
            ax2.legend()

            # Third column: Scatter plot of Normalized calculated curve vs Target Log
            ax3.scatter(np.ma.masked_array(normalized_eei, ~valid_mask),
                        np.ma.masked_array(target, ~valid_mask),
                        alpha=0.5)
            ax3.set_xlabel(f'{analysis_type} ({angle_display})')
            ax3.set_ylabel(target_log)

            # Calculate optimal axis limits using advanced scaling algorithm with global percentiles fallback
            try:
                crossplot_x_min, crossplot_x_max, crossplot_y_min, crossplot_y_max = self.calculate_optimal_crossplot_limits(
                    normalized_eei[valid_mask],
                    target[valid_mask],
                    correlation,
                    global_x_min=global_min,
                    global_x_max=global_max,
                    global_y_min=global_min,  # Use same global percentiles for y-axis
                    global_y_max=global_max,
                    well_name=well_name
                )

                # Validate that final limits are finite before setting
                if not all(np.isfinite([crossplot_x_min, crossplot_x_max, crossplot_y_min, crossplot_y_max])):
                    logger.warning(f"Crossplot limits are not finite for well {well_name}: "
                                  f"x_min={crossplot_x_min}, x_max={crossplot_x_max}, y_min={crossplot_y_min}, y_max={crossplot_y_max}. "
                                  f"Using global percentiles as fallback.")
                    crossplot_x_min, crossplot_x_max, crossplot_y_min, crossplot_y_max = global_min, global_max, global_min, global_max

                ax3.set_xlim(crossplot_x_min, crossplot_x_max)
                ax3.set_ylim(crossplot_y_min, crossplot_y_max)
                logger.debug(f"Set crossplot limits for well {well_name}: "
                            f"x_min={crossplot_x_min:.4f}, x_max={crossplot_x_max:.4f}, y_min={crossplot_y_min:.4f}, y_max={crossplot_y_max:.4f}")

            except Exception as e:
                logger.error(f"Error setting crossplot limits for well {well_name}: {str(e)}. Using global percentiles.")
                ax3.set_xlim(global_min, global_max)
                ax3.set_ylim(global_min, global_max)

            # Calculate linear regression
            x_for_regression = normalized_eei[valid_mask]
            y_for_regression = target[valid_mask]
            if len(x_for_regression) > 1:  # Need at least 2 points for regression
                # Ensure no NaN values in the data
                reg_mask = ~np.isnan(x_for_regression) & ~np.isnan(y_for_regression)
                if np.sum(reg_mask) > 1:  # Still need at least 2 valid points after removing NaNs
                    regression = stats.linregress(x_for_regression[reg_mask], y_for_regression[reg_mask])
                    # Plot regression line using optimized axis limits
                    x_line = np.array([crossplot_x_min, crossplot_x_max])
                    y_line = regression.slope * x_line + regression.intercept
                    ax3.plot(x_line, y_line, color=self.color_schemes['default']['regression_line'],
                            linewidth=2, label='Linear regression')

                    # Add regression equation and R² to the plot
                    equation = f'y = {regression.slope:.4f}x + {regression.intercept:.4f}'
                    r_squared = regression.rvalue**2
                    ax3.text(0.05, 0.85, equation, transform=ax3.transAxes, verticalalignment='top')
                    ax3.text(0.05, 0.75, f'R² = {r_squared:.4f}', transform=ax3.transAxes, verticalalignment='top')

            # Add a diagonal line (y = x) for reference using the appropriate range
            diag_min = max(crossplot_x_min, crossplot_y_min)
            diag_max = min(crossplot_x_max, crossplot_y_max)
            if diag_min < diag_max:  # Only draw if there's a valid range
                ax3.plot([diag_min, diag_max], [diag_min, diag_max],
                        color=self.color_schemes['default']['diagonal_line'],
                        linestyle='--', alpha=0.5, label='y = x')

            # Add correlation value to the plot
            ax3.text(0.05, 0.95, f'Correlation: {safe_format_float(correlation, precision=4, default="N/A")}',
                     transform=ax3.transAxes, verticalalignment='top')

            # Add legend
            ax3.legend(loc='lower right')

            # Add correlation value to the plot title
            fig.suptitle(f'{well_name}: {analysis_type} vs {target_log} Analysis\nCorrelation: {safe_format_float(correlation, precision=4, default="N/A")}', fontsize=16)

            plt.tight_layout()
            plt.show()

        logger.info(f"Completed plotting for {len(all_wells_data)} wells")

    def plot_correlation_vs_angle(self, angles, correlations, optimum_angle, target_log_generic_name,
                                 well_name=None, top_depth=None, bottom_depth=None):
        """
        Plot EEI-Target Log Correlation vs Angle.

        Args:
            angles: Array of angles tested
            correlations: Array of correlation coefficients
            optimum_angle: Optimal angle value
            target_log_generic_name: Name of target log
            well_name: Well name (None for merged wells)
            top_depth: Top depth for individual wells (optional)
            bottom_depth: Bottom depth for individual wells (optional)
        """
        plt.figure(figsize=self.plot_settings['figure_size_correlation'])
        plt.plot(angles, correlations)
        plt.axvline(x=optimum_angle, color='r', linestyle='--', label=f'Optimum Angle: {optimum_angle}°')
        plt.xlabel('Angle (degrees)')
        plt.ylabel('Correlation Coefficient')

        # Create title based on whether it's individual well or merged wells
        if well_name is not None:
            if top_depth is not None and bottom_depth is not None:
                title = f'EEI-{target_log_generic_name} Correlation vs Angle for Well: {well_name}\nDepth range: {safe_format_float(top_depth, precision=2, default="N/A")} - {safe_format_float(bottom_depth, precision=2, default="N/A")}'
            else:
                title = f'EEI-{target_log_generic_name} Correlation vs Angle for Well: {well_name}'
        else:
            title = f'EEI-{target_log_generic_name} Correlation vs Angle for Merged Wells'

        plt.title(title)
        plt.legend()
        plt.grid(True)
        plt.show()

        logger.debug(f"Plotted correlation vs angle for {'well ' + well_name if well_name else 'merged wells'}")

    def plot_correlation_heatmap(self, correlation_matrix, phi_values, n_values, optimal_n, optimal_phi,
                                max_correlation, target_log_generic_name, analysis_type, well_name=None,
                                top_depth=None, bottom_depth=None):
        """
        Plot CPEI/PEIL correlation heatmap.

        Args:
            correlation_matrix: 2D array of correlation values
            phi_values: Array of phi (angle) values
            n_values: Array of n (exponent) values
            optimal_n: Optimal n value
            optimal_phi: Optimal phi value
            max_correlation: Maximum correlation value
            target_log_generic_name: Name of target log
            analysis_type: 'CPEI' or 'PEIL'
            well_name: Well name (None for merged wells)
            top_depth: Top depth for individual wells (optional)
            bottom_depth: Bottom depth for individual wells (optional)
        """
        plt.figure(figsize=self.plot_settings['figure_size_heatmap'])
        plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
        plt.colorbar(label='Correlation Coefficient')
        plt.xlabel('Phi (degrees)')
        plt.ylabel('n (exponent)')

        # Create title based on whether it's individual well or merged wells
        if well_name is not None:
            if top_depth is not None and bottom_depth is not None:
                title = f'{analysis_type}-{target_log_generic_name} Correlation Matrix for Well: {well_name}\nDepth range: {safe_format_float(top_depth, precision=2, default="N/A")} - {safe_format_float(bottom_depth, precision=2, default="N/A")}'
            else:
                title = f'{analysis_type}-{target_log_generic_name} Correlation Matrix for Well: {well_name}'
        else:
            title = f'{analysis_type}-{target_log_generic_name} Correlation Matrix for Merged Wells'

        plt.title(title)

        # Set tick labels
        phi_ticks = range(0, len(phi_values), 30)  # Every 30 degrees
        n_ticks = range(0, len(n_values), 5)  # Every 0.5 in n
        plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
        plt.yticks(n_ticks, [safe_format_float(n_values[i], precision=1, default='N/A') for i in n_ticks])

        # Mark the optimal point
        try:
            optimal_phi_idx = np.argmin(np.abs(np.array(phi_values) - optimal_phi))
            optimal_n_idx = np.argmin(np.abs(np.array(n_values) - optimal_n))
            plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15, label='Optimal Point')
            plt.legend()
        except Exception as e:
            logger.warning(f"Could not mark optimal point on heatmap: {str(e)}")

        # Format max correlation for display
        max_corr_text = safe_format_float(max_correlation, precision=4, default="N/A")

        # Add summary text box in the corner with appropriate color scheme
        if well_name is not None:
            summary_text = f'{analysis_type} Individual Well Summary:\n'
            summary_text += f'Well: {well_name}\n'
        else:
            summary_text = f'{analysis_type} Merged Analysis Summary:\n'

        summary_text += f'Optimal n: {safe_format_float(optimal_n, precision=1, default="N/A")}\n'
        summary_text += f'Optimal φ: {safe_format_float(optimal_phi, precision=0, default="N/A")}°\n'
        summary_text += f'Max Correlation: {max_corr_text}\n'
        summary_text += f'Target: {target_log_generic_name}'

        # Choose color based on analysis type
        box_color = "lightblue" if analysis_type == "CPEI" else "lightgreen"

        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor=box_color, alpha=0.8),
                verticalalignment='top', fontsize=9, fontfamily='monospace')

        plt.show()

        logger.debug(f"Plotted {analysis_type} correlation heatmap for {'well ' + well_name if well_name else 'merged wells'}")

    def plot_summary_chart(self, results_data, analysis_type, target_log):
        """
        Plot summary chart for multiple wells analysis results.

        Args:
            results_data: List of dictionaries with well results
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            target_log: Name of target log
        """
        if not results_data:
            print("No valid results to plot summary chart.")
            return

        fig, ax1 = plt.subplots(figsize=(14, 8))
        well_names = [result['well_name'] for result in results_data]

        if analysis_type == 'EEI':
            # For EEI, plot optimum angles and correlations
            optimum_angles = [result['optimum_angle'] for result in results_data]
            max_correlations = [result['max_correlation'] for result in results_data]

            ax1.set_xlabel('Well Name')
            ax1.set_ylabel('Optimum Angle (degrees)', color='b')
            ax1.plot(well_names, optimum_angles, 'b-o', label='Optimum Angle')
            ax1.tick_params(axis='y', labelcolor='b')
            ax1.set_ylim(0, 90)

            # Create second y-axis for correlation
            ax2 = ax1.twinx()
            ax2.set_ylabel('Max Correlation', color='r')
            ax2.plot(well_names, max_correlations, 'r-s', label='Max Correlation')
            ax2.tick_params(axis='y', labelcolor='r')
            ax2.set_ylim(0, 1)

            plt.title(f'EEI Analysis Summary - Optimum Angles and Correlations vs {target_log}')

        else:
            # For CPEI/PEIL, plot optimal parameters
            optimal_n_values = [result.get('optimal_n', 0) for result in results_data]
            optimal_phi_values = [result.get('optimal_phi', 0) for result in results_data]
            max_correlations = [result['max_correlation'] for result in results_data]

            ax1.set_xlabel('Well Name')
            ax1.set_ylabel('Optimal n', color='b')
            ax1.plot(well_names, optimal_n_values, 'b-o', label='Optimal n')
            ax1.tick_params(axis='y', labelcolor='b')

            # Create second y-axis for phi
            ax2 = ax1.twinx()
            ax2.set_ylabel('Optimal φ (degrees)', color='g')
            ax2.plot(well_names, optimal_phi_values, 'g-^', label='Optimal φ')
            ax2.tick_params(axis='y', labelcolor='g')

            plt.title(f'{analysis_type} Analysis Summary - Optimal Parameters vs {target_log}')

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.show()

        logger.info(f"Plotted summary chart for {len(results_data)} wells ({analysis_type} analysis)")


# Global instance for backward compatibility
_plotting_components_instance = None

def get_plotting_components():
    """Get the global plotting components instance."""
    global _plotting_components_instance
    if _plotting_components_instance is None:
        _plotting_components_instance = PlottingComponents()
    return _plotting_components_instance


# Legacy wrapper functions for backward compatibility
def plot_eei_vs_target(all_wells_data, target_log, depth_ranges):
    """
    Legacy wrapper function for backward compatibility.

    This function maintains the exact same interface as the original function
    in the main file, ensuring no breaking changes.
    """
    plotter = get_plotting_components()
    return plotter.plot_eei_vs_target(all_wells_data, target_log, depth_ranges)


def calculate_global_percentiles_for_axis_limits(all_wells_data):
    """
    Legacy wrapper function for backward compatibility.

    This function maintains the exact same interface as the original function
    in the main file, ensuring no breaking changes.
    """
    plotter = get_plotting_components()
    return plotter.calculate_global_percentiles_for_axis_limits(all_wells_data)


def calculate_optimal_crossplot_limits(x_data, y_data, correlation=None,
                                     global_x_min=None, global_x_max=None,
                                     global_y_min=None, global_y_max=None,
                                     well_name="Unknown"):
    """
    Legacy wrapper function for backward compatibility.

    This function maintains the exact same interface as the original function
    in the main file, ensuring no breaking changes.
    """
    plotter = get_plotting_components()
    return plotter.calculate_optimal_crossplot_limits(
        x_data, y_data, correlation, global_x_min, global_x_max,
        global_y_min, global_y_max, well_name
    )


def plot_correlation_vs_angle(angles, correlations, optimum_angle, target_log_generic_name,
                             well_name=None, top_depth=None, bottom_depth=None):
    """
    Legacy wrapper function for backward compatibility.

    Plot EEI-Target Log Correlation vs Angle.
    """
    plotter = get_plotting_components()
    return plotter.plot_correlation_vs_angle(
        angles, correlations, optimum_angle, target_log_generic_name,
        well_name, top_depth, bottom_depth
    )


def plot_correlation_heatmap(correlation_matrix, phi_values, n_values, optimal_n, optimal_phi,
                            max_correlation, target_log_generic_name, analysis_type, well_name=None,
                            top_depth=None, bottom_depth=None):
    """
    Legacy wrapper function for backward compatibility.

    Plot CPEI/PEIL correlation heatmap.
    """
    plotter = get_plotting_components()
    return plotter.plot_correlation_heatmap(
        correlation_matrix, phi_values, n_values, optimal_n, optimal_phi,
        max_correlation, target_log_generic_name, analysis_type, well_name,
        top_depth, bottom_depth
    )


def plot_summary_chart(results_data, analysis_type, target_log):
    """
    Legacy wrapper function for backward compatibility.

    Plot summary chart for multiple wells analysis results.
    """
    plotter = get_plotting_components()
    return plotter.plot_summary_chart(results_data, analysis_type, target_log)


def plot_statistical_analysis(all_wells_data, all_wells_results, analysis_type):
    """
    Plot comprehensive statistical analysis including histograms and distributions.
    
    Args:
        all_wells_data: List of well data dictionaries
        all_wells_results: List of optimization results for each well
        analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
    """
    if not STATISTICAL_ANALYSIS_AVAILABLE:
        logger.warning("Statistical analysis module not available. Skipping statistical plots.")
        print("⚠️  Statistical analysis module not available. Skipping statistical plots.")
        return
    
    try:
        print(f"📊 Initializing statistical analyzer...")
        analyzer = get_statistical_analyzer()
        
        # Analyze distributions and parameters
        logger.info(f"Starting comprehensive statistical analysis for {analysis_type}")
        print(f"🔍 Analyzing data distributions for {len(all_wells_data)} wells...")
        
        dist_analysis = analyzer.analyze_well_data_distributions(all_wells_data, analysis_type)
        print(f"📈 Analyzing optimization parameters for {len(all_wells_results)} results...")
        param_analysis = analyzer.analyze_optimization_parameters(all_wells_results, analysis_type)
        
        # Plot distributions
        print(f"📊 Generating distribution plots...")
        analyzer.plot_value_distributions(dist_analysis)
        
        print(f"📊 Generating parameter distribution plots...")
        analyzer.plot_parameter_distributions(param_analysis)
        
        print(f"📊 Generating well comparison plots...")
        analyzer.plot_well_comparison_boxplots(all_wells_data, analysis_type)
        
        # Generate and print report
        print(f"📋 Generating statistical report...")
        report = analyzer.generate_statistical_report(dist_analysis, param_analysis)
        print(report)
        
        logger.info(f"Completed statistical analysis for {analysis_type}")
        print(f"✅ Statistical analysis completed successfully for {analysis_type}")
        
    except Exception as e:
        logger.error(f"Error in statistical analysis: {str(e)}")
        print(f"❌ Error in statistical analysis: {str(e)}")
        import traceback
        traceback.print_exc()


def plot_distribution_histograms(all_wells_data, analysis_type):
    """
    Plot distribution histograms for calculated values.
    
    Args:
        all_wells_data: List of well data dictionaries
        analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
    """
    if not STATISTICAL_ANALYSIS_AVAILABLE:
        logger.warning("Statistical analysis module not available. Skipping histogram plots.")
        return
    
    try:
        analyzer = get_statistical_analyzer()
        dist_analysis = analyzer.analyze_well_data_distributions(all_wells_data, analysis_type)
        analyzer.plot_value_distributions(dist_analysis)
        
    except Exception as e:
        logger.error(f"Error plotting distribution histograms: {str(e)}")


def plot_parameter_histograms(all_wells_results, analysis_type):
    """
    Plot histograms for optimization parameters.
    
    Args:
        all_wells_results: List of optimization results for each well
        analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
    """
    if not STATISTICAL_ANALYSIS_AVAILABLE:
        logger.warning("Statistical analysis module not available. Skipping parameter histograms.")
        return
    
    try:
        analyzer = get_statistical_analyzer()
        param_analysis = analyzer.analyze_optimization_parameters(all_wells_results, analysis_type)
        analyzer.plot_parameter_distributions(param_analysis)
        
    except Exception as e:
        logger.error(f"Error plotting parameter histograms: {str(e)}")


def plot_well_comparison_boxplots(all_wells_data, analysis_type):
    """
    Plot box plots comparing values across wells.
    
    Args:
        all_wells_data: List of well data dictionaries
        analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
    """
    if not STATISTICAL_ANALYSIS_AVAILABLE:
        logger.warning("Statistical analysis module not available. Skipping box plots.")
        return
    
    try:
        analyzer = get_statistical_analyzer()
        analyzer.plot_well_comparison_boxplots(all_wells_data, analysis_type)
        
    except Exception as e:
        logger.error(f"Error plotting well comparison box plots: {str(e)}")
