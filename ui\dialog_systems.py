# -*- coding: utf-8 -*-
"""
Dialog Systems Module

Centralized user input dialogs for EEI analysis workflow.
Handles parameter selection, depth ranges, target logs, and workflow decisions.

This module extracts all dialog-related functionality from the main application
to improve maintainability and enable better testing.
"""

import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import numpy as np
import logging

# Import helper functions for get_depth_ranges
from ui.file_management import (
    find_default_columns, load_boundaries_from_excel,
    filter_excel_data_for_las_wells
)

logger = logging.getLogger(__name__)


class DialogSystems:
    """
    Centralized dialog management for EEI analysis workflow.

    This class manages all user input dialogs including:
    - Analysis type and parameter selection
    - Depth range selection with Excel integration
    - Target log selection interface
    - Post-analysis action selection
    """

    def __init__(self):
        """Initialize dialog systems with state management."""
        self.last_selections = {}  # Cache user selections for consistency
        self.state = {}  # Shared state for dialog coordination

    def get_analysis_type_and_parameters(self):
        """
        Prompt the user to select the analysis type (EEI, CPEI, or PEIL) and associated parameters.

        Returns:
            tuple: (analysis_method, calcmethod, k_method, k_value)
                - analysis_method: 1=EEI, 2=CPEI, 3=PEIL
                - calcmethod: EEI calculation method (1-3) or None
                - k_method: K value determination method (1-2) or None
                - k_value: Constant k value or None
        """
        root = tk.Tk()
        root.withdraw()

        # Ask user to select analysis type
        analysis_method = simpledialog.askinteger(
            "Analysis Type Selection",
            "Select the analysis type:\n"
            "1: EEI (Extended Elastic Impedance) Analysis\n"
            "2: CPEI (Compressional Poisson Elastic Impedance) Analysis\n"
            "3: PEIL (Poisson Elastic Impedance Log) Analysis",
            minvalue=1, maxvalue=3
        )

        if analysis_method not in [1, 2, 3]:
            print("Invalid analysis type. Using default EEI analysis.")
            analysis_method = 1

        # Initialize return values
        calcmethod = None
        k_method = None
        k_value = None

        if analysis_method == 1:  # EEI Analysis
            print("Selected: EEI (Extended Elastic Impedance) Analysis")

            # Ask for EEI calculation method
            calcmethod = simpledialog.askinteger(
                "EEI Calculation Method",
                "Enter the calcmethod for EEI calculation:\n"
                "1: EEI using Vp, Vs, density\n"
                "2: EEI using AI, SI, density\n"
                "3: EEI using AI, SI, with omitted density",
                minvalue=1, maxvalue=3
            )
            if calcmethod not in [1, 2, 3]:
                print("Invalid calcmethod. Using default value 3.")
                calcmethod = 3

            # Ask user how to determine k value
            k_method = simpledialog.askinteger(
                "K Value Method",
                "How would you like to determine k value?\n"
                "1: Calculate from average of logs (Vs/Vp)²\n"
                "2: Enter a constant value manually",
                minvalue=1, maxvalue=2
            )

            # If user chose to enter a constant value
            if k_method == 2:
                k_value = simpledialog.askfloat(
                    "K Value Input",
                    "Enter constant k value (typically between 0.1 and 0.5):",
                    minvalue=0.01, maxvalue=1.0
                )
                if k_value is None:
                    print("Invalid k value. Using default method (calculate from logs).")
                    k_method = 1

        elif analysis_method == 2:  # CPEI Analysis
            print("Selected: CPEI (Compressional Poisson Elastic Impedance) Analysis")
            print("CPEI will search for optimal n (0.1-2.0) and phi (-90° to +90°) parameters.")

        elif analysis_method == 3:  # PEIL Analysis
            print("Selected: PEIL (Poisson Elastic Impedance Log) Analysis")
            print("PEIL will search for optimal n (0.1-2.0) and phi (-90° to +90°) parameters.")

        # Cache the selection for consistency
        self.last_selections['analysis_type'] = {
            'method': analysis_method,
            'calcmethod': calcmethod,
            'k_method': k_method,
            'k_value': k_value
        }

        return analysis_method, calcmethod, k_method, k_value

    def show_next_action_dialog(self):
        """
        Show a dialog asking the user what to do after analysis completion.

        Returns:
            str: 'restart' to start new analysis, 'exit' to exit program
        """
        root = tk.Tk()
        root.withdraw()

        # Create custom dialog
        dialog = tk.Toplevel()
        dialog.title("Analysis Complete")
        dialog.geometry("400x200")
        dialog.grab_set()  # Make dialog modal
        dialog.resizable(False, False)

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = {'action': 'exit'}

        # Main message
        message_frame = tk.Frame(dialog)
        message_frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)

        title_label = tk.Label(
            message_frame,
            text="🎉 Analysis Complete!",
            font=("Arial", 14, "bold"),
            fg="green"
        )
        title_label.pack(pady=(0, 10))

        message_label = tk.Label(
            message_frame,
            text="The EEI cross-correlation analysis has been completed successfully.\n"
                 "What would you like to do next?",
            font=("Arial", 10),
            justify=tk.CENTER,
            wraplength=350
        )
        message_label.pack(pady=(0, 20))

        # Button frame
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=(0, 20), padx=20, fill=tk.X)

        def restart_analysis():
            result['action'] = 'restart'
            dialog.destroy()

        def exit_program():
            result['action'] = 'exit'
            dialog.destroy()

        # Restart button
        restart_btn = tk.Button(
            button_frame,
            text="🔄 Start New Analysis",
            command=restart_analysis,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold"),
            padx=20,
            pady=5
        )
        restart_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

        # Exit button
        exit_btn = tk.Button(
            button_frame,
            text="🚪 Exit Program",
            command=exit_program,
            bg="#f44336",
            fg="white",
            font=("Arial", 10, "bold"),
            padx=20,
            pady=5
        )
        exit_btn.pack(side=tk.RIGHT, padx=(10, 0), fill=tk.X, expand=True)

        # Handle window close event
        dialog.protocol("WM_DELETE_WINDOW", exit_program)

        # Wait for dialog to close
        dialog.wait_window()

        return result['action']

    def select_alternative_mnemonic(self, las, missing_target_log, well_name):
        """
        Display a dialog showing all available curve mnemonics in a specific well
        and allow the user to select an alternative mnemonic to use as a substitute
        for the missing target log.

        Args:
            las: The LAS file object
            missing_target_log: The name of the missing target log
            well_name: The name of the well being processed

        Returns:
            The selected alternative mnemonic or None if the user chooses to skip
        """
        root = tk.Tk()
        root.title(f"Select Alternative for '{missing_target_log}' in Well '{well_name}'")
        root.geometry("500x400")  # Larger size for better visibility

        frame = ttk.Frame(root, padding="10")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)

        # Create header label with detailed information
        header_text = (f"Target log '{missing_target_log}' is missing in well '{well_name}'.\n"
                      f"Please select an alternative curve to use, or click 'Skip Well' to exclude this well from analysis.")
        header_label = ttk.Label(frame, text=header_text, wraplength=480, justify="center")
        header_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))

        # Create a frame for the listbox and its scrollbar
        list_frame = ttk.Frame(frame)
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # Create Listbox with curve information
        columns = ("Mnemonic", "Unit", "Description")
        tree = ttk.Treeview(list_frame, columns=columns, show="headings", selectmode="browse")

        # Set column headings
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(list_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=vsb.set)

        # Grid the treeview and scrollbar
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Populate the treeview with curve information
        for curve in las.curves:
            mnemonic = curve.mnemonic
            unit = curve.unit if hasattr(curve, 'unit') else ""
            descr = curve.descr if hasattr(curve, 'descr') else ""
            tree.insert("", "end", values=(mnemonic, unit, descr))

        # Variables to store the result
        result = {"mnemonic": None}

        # Define button actions
        def on_select():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                result["mnemonic"] = item["values"][0]  # Get the mnemonic
            root.quit()

        def on_skip():
            result["mnemonic"] = None
            root.quit()

        # Create buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(15, 0))

        select_button = ttk.Button(button_frame, text="Use Selected Curve", command=on_select)
        select_button.grid(row=0, column=0, padx=5)

        skip_button = ttk.Button(button_frame, text="Skip Well", command=on_skip)
        skip_button.grid(row=0, column=1, padx=5)

        # Configure frame weights
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(1, weight=1)

        # Start the dialog
        root.mainloop()
        root.destroy()

        return result["mnemonic"]

    def get_target_log(self, available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used=False):
        """
        Create a scrollable list for target log selection, highlighting logs present in all wells.

        Args:
            available_logs: List of available log names
            common_actual_mnemonics: Set of mnemonics available in all wells
            common_generic_keywords: Set of generic keywords available in all wells
            calculator_used: Boolean indicating if custom calculator was used
        """
        root = tk.Tk()
        root.title("Select Target Log")
        root.geometry("350x450")  # Increased window size for additional information

        frame = ttk.Frame(root, padding="10")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)

        label = ttk.Label(frame, text="Select the log to correlate with EEI:")
        label.grid(row=0, column=0, pady=(0, 5))

        # Enhanced explanatory label with calculator information
        if calculator_used:
            info_text = ("★ = Available in all wells (SAFE for analysis)\n"
                        "Regular = Available in some wells only\n"
                        "🧮 = Custom calculated logs (newly created)")
            info_label = ttk.Label(frame, text=info_text, font=("", 8, "italic"),
                                  foreground='darkblue', justify=tk.LEFT)
        else:
            info_text = ("★ = Available in all wells (SAFE for analysis)\n"
                        "Regular = Available in some wells only")
            info_label = ttk.Label(frame, text=info_text, font=("", 8, "italic"),
                                  foreground='darkblue', justify=tk.LEFT)

        info_label.grid(row=1, column=0, pady=(0, 10))

        # Create Listbox
        listbox = tk.Listbox(frame, selectmode=tk.SINGLE, exportselection=0)
        listbox.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Add a scrollbar
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=listbox.yview)
        scrollbar.grid(row=2, column=1, sticky=(tk.N, tk.S))
        listbox.configure(yscrollcommand=scrollbar.set)

        # Create a mapping between display text and actual log name
        display_to_actual = {}

        # Identify newly created logs (if calculator was used)
        newly_created_logs = set()
        if calculator_used:
            # Common patterns for newly created logs
            calc_patterns = ['_HC', '_RATIO', '_NORM', 'AI', 'POISSON', 'KDKS', 'GDGS',
                            'PHIE_1_SWE', 'PHIT_1_SWT', 'NPHI_SHC']
            for log in available_logs:
                if any(pattern in log.upper() for pattern in calc_patterns):
                    newly_created_logs.add(log)

        # Populate the Listbox and apply highlighting
        for log in available_logs:
            # Check if the log is common (either actual mnemonic or generic keyword)
            is_common = (log.upper() in common_actual_mnemonics) or (log in common_generic_keywords)
            is_newly_created = log in newly_created_logs

            if is_common and is_newly_created:
                # Newly created and available in all wells
                display_text = f"★🧮 {log}"
                listbox.insert(tk.END, display_text)
                listbox.itemconfigure(tk.END, foreground='#0066CC')  # Blue for new calculated logs
                display_to_actual[display_text] = log
            elif is_common:
                # Available in all wells
                display_text = f"★ {log}"
                listbox.insert(tk.END, display_text)
                listbox.itemconfigure(tk.END, foreground='#00CC00')  # Green for safe logs
                display_to_actual[display_text] = log
            elif is_newly_created:
                # Newly created but not in all wells
                display_text = f"🧮 {log}"
                listbox.insert(tk.END, display_text)
                listbox.itemconfigure(tk.END, foreground='#FF6600')  # Orange for partial new logs
                display_to_actual[display_text] = log
            else:
                # Regular logs without prefix
                listbox.insert(tk.END, log)
                display_to_actual[log] = log

        # Select the first item by default
        if available_logs:
            listbox.selection_set(0)

        selected_log = tk.StringVar()

        def on_select():
            selected_indices = listbox.curselection()
            if selected_indices:
                display_text = listbox.get(selected_indices[0])
                # Convert display text back to actual log name
                actual_log = display_to_actual[display_text]
                selected_log.set(actual_log)
            root.quit()

        select_button = ttk.Button(frame, text="Select", command=on_select)
        select_button.grid(row=3, column=0, pady=(10, 0))

        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(2, weight=1)

        root.mainloop()
        root.destroy()

        return selected_log.get()

    def select_boundaries_from_excel(self, df, well_name):
        """
        Create a dialog to select top and bottom boundaries from Excel data for a specific well.
        The dialog will only show boundaries for the specified well.

        Args:
            df: DataFrame containing boundary data
            well_name: Name of the well to filter data for

        Returns:
            Tuple of (top_depth, bottom_depth) or None if cancelled
        """
        # Filter data for the current well
        well_data = df[df['Well'] == well_name]

        if well_data.empty:
            messagebox.showerror(
                "Missing Well Data",
                f"No boundary data found for well '{well_name}' in the Excel file."
            )
            return None

        # Sort the data by depth to make selection more intuitive
        well_data = well_data.sort_values('MD')

        # Create dialog
        dialog = tk.Toplevel()
        dialog.title(f"Select Boundaries for {well_name}")
        dialog.geometry("400x300")

        # Create frame
        frame = ttk.Frame(dialog, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        # Get unique surface names for this well
        surfaces = well_data['Surface'].unique().tolist()

        # Create variables to store selections
        top_surface_var = tk.StringVar()
        bottom_surface_var = tk.StringVar()

        # Set default values if available
        if len(surfaces) > 0:
            top_surface_var.set(surfaces[0])
        if len(surfaces) > 1:
            bottom_surface_var.set(surfaces[-1])

        # Function to update depth labels when surface selection changes
        def update_depth_labels(*args):
            top_surface = top_surface_var.get()
            bottom_surface = bottom_surface_var.get()

            top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
            bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

            if len(top_md) > 0:
                top_depth_label.config(text=f"Depth: {top_md[0]:.2f}")
            else:
                top_depth_label.config(text="Depth: N/A")

            if len(bottom_md) > 0:
                bottom_depth_label.config(text=f"Depth: {bottom_md[0]:.2f}")
            else:
                bottom_depth_label.config(text="Depth: N/A")

        # Create widgets
        ttk.Label(frame, text="Select Top Boundary:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        top_combo = ttk.Combobox(frame, textvariable=top_surface_var, values=surfaces, state="readonly")
        top_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        top_depth_label = ttk.Label(frame, text="Depth: ")
        top_depth_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        ttk.Label(frame, text="Select Bottom Boundary:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        bottom_combo = ttk.Combobox(frame, textvariable=bottom_surface_var, values=surfaces, state="readonly")
        bottom_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        bottom_depth_label = ttk.Label(frame, text="Depth: ")
        bottom_depth_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Register callbacks
        top_surface_var.trace_add("write", update_depth_labels)
        bottom_surface_var.trace_add("write", update_depth_labels)

        # Initialize depth labels
        update_depth_labels()

        # Result variable
        result = {"top_depth": None, "bottom_depth": None, "cancelled": False}

        def on_ok():
            # Show processing indicator
            status_label = ttk.Label(frame, text="Processing...", font=("", 9, "italic"))
            status_label.grid(row=5, column=0, columnspan=2, pady=(5, 0))
            dialog.update_idletasks()  # Force UI update

            top_surface = top_surface_var.get()
            bottom_surface = bottom_surface_var.get()

            top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
            bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

            if len(top_md) == 0 or len(bottom_md) == 0:
                status_label.destroy()
                messagebox.showerror("Missing Data", "Could not retrieve depth values for selected surfaces.")
                return

            result["top_depth"] = float(top_md[0])
            result["bottom_depth"] = float(bottom_md[0])

            # Update status and close dialog
            status_label.config(text="Selection complete! Closing...")
            dialog.update_idletasks()  # Force UI update

            # Use after() to ensure the UI updates before destroying the dialog
            dialog.after(300, lambda: dialog.destroy())

        def on_cancel():
            result["cancelled"] = True
            dialog.destroy()

        # Create buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

        # Configure grid weights
        frame.columnconfigure(1, weight=1)

        # Run dialog
        dialog.protocol("WM_DELETE_WINDOW", on_cancel)
        dialog.transient()
        dialog.grab_set()

        # Make the dialog modal and wait for it to be destroyed
        dialog.wait_visibility()
        dialog.focus_set()
        dialog.wait_window()

        if result["cancelled"]:
            print(f"Selection cancelled for well {well_name}.")
            return None

        print(f"Selection completed for well {well_name}: ({result['top_depth']:.2f}, {result['bottom_depth']:.2f})")
        return (result["top_depth"], result["bottom_depth"])

    def _find_common_surfaces(self, df, las_well_names):
        """
        Find surfaces that appear across multiple wells, sorted by frequency.
        
        Args:
            df: DataFrame containing boundary data
            las_well_names: List of well names from LAS files
            
        Returns:
            List of tuples: (surface_name, well_count, wells_list)
        """
        available_wells = [well for well in las_well_names if well in df['Well'].unique()]
        
        if not available_wells:
            return []
            
        # Count surface occurrences across wells
        surface_counts = {}
        surface_wells = {}
        
        for well in available_wells:
            well_data = df[df['Well'] == well]
            if not well_data.empty:
                surfaces = well_data['Surface'].unique()
                for surface in surfaces:
                    if surface not in surface_counts:
                        surface_counts[surface] = 0
                        surface_wells[surface] = []
                    surface_counts[surface] += 1
                    surface_wells[surface].append(well)
        
        # Sort by frequency (most common first)
        common_surfaces = [(surface, count, surface_wells[surface]) 
                          for surface, count in sorted(surface_counts.items(), 
                                                      key=lambda x: x[1], reverse=True)]
        
        return common_surfaces

    def _select_boundaries_for_all_wells(self, df, las_well_names):
        """
        Helper method: Create a dialog to select top and bottom boundaries for all wells at once.
        Now includes common boundary selection functionality.

        Args:
            df: DataFrame containing boundary data
            las_well_names: List of well names from LAS files

        Returns:
            Dictionary mapping well names to (top_depth, bottom_depth) tuples,
            or None if cancelled
        """
        # Filter data for wells that exist in both the Excel file and LAS files
        available_wells = [well for well in las_well_names if well in df['Well'].unique()]

        if not available_wells:
            messagebox.showerror(
                "No Matching Wells",
                "No wells in the Excel file match the loaded LAS files."
            )
            return None

        # Find common surfaces across wells
        common_surfaces = self._find_common_surfaces(df, las_well_names)
        
        # Create a dictionary to store well data
        well_data_dict = {}
        for well in available_wells:
            # Filter and sort data for this well
            well_data = df[df['Well'] == well].sort_values('MD')
            if not well_data.empty:
                well_data_dict[well] = well_data

        # Create dialog
        dialog = tk.Toplevel()
        dialog.title("Select Boundaries for All Wells")
        dialog.geometry("900x700")  # Larger dialog for the enhanced interface

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Add instructions
        instructions = ttk.Label(
            main_frame,
            text="Select top and bottom boundaries for all wells. You can use common boundaries for all wells or set individual boundaries.",
            wraplength=880
        )
        instructions.pack(pady=(0, 10))

        # Common Boundaries Section
        common_frame = ttk.LabelFrame(main_frame, text="Common Boundaries", padding="10")
        common_frame.pack(fill=tk.X, pady=(0, 10))

        # Variables for common boundary functionality
        use_common_var = tk.BooleanVar()
        common_top_var = tk.StringVar()
        common_bottom_var = tk.StringVar()

        # Create list of common surface options with frequency info
        common_surface_options = []
        if common_surfaces:
            for surface, count, wells in common_surfaces:
                if count >= 2:  # Only show surfaces that appear in at least 2 wells
                    display_text = f"{surface} ({count}/{len(available_wells)} wells)"
                    common_surface_options.append((surface, display_text))

        # Common boundaries checkbox
        common_check = ttk.Checkbutton(
            common_frame, 
            text="Use common boundaries for all wells",
            variable=use_common_var
        )
        common_check.grid(row=0, column=0, columnspan=4, sticky="w", pady=(0, 10))

        # Common boundary controls
        ttk.Label(common_frame, text="Common Top Boundary:").grid(row=1, column=0, sticky="w", padx=(20, 5))
        common_top_combo = ttk.Combobox(
            common_frame, 
            textvariable=common_top_var,
            values=[display for _, display in common_surface_options],
            state="disabled",
            width=25
        )
        common_top_combo.grid(row=1, column=1, sticky="w", padx=5)

        ttk.Label(common_frame, text="Common Bottom Boundary:").grid(row=1, column=2, sticky="w", padx=(20, 5))
        common_bottom_combo = ttk.Combobox(
            common_frame, 
            textvariable=common_bottom_var,
            values=[display for _, display in common_surface_options],
            state="disabled",
            width=25
        )
        common_bottom_combo.grid(row=1, column=3, sticky="w", padx=5)

        # Apply common boundaries button
        apply_common_btn = ttk.Button(
            common_frame,
            text="Apply to All Wells",
            state="disabled"
        )
        apply_common_btn.grid(row=2, column=0, columnspan=4, pady=(10, 0))

        # Status label for common boundaries
        common_status_var = tk.StringVar()
        common_status_label = ttk.Label(common_frame, textvariable=common_status_var, font=("", 9, "italic"))
        common_status_label.grid(row=3, column=0, columnspan=4, pady=(5, 0))

        # Individual Wells Section
        wells_frame = ttk.LabelFrame(main_frame, text="Individual Well Boundaries", padding="10")
        wells_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Create scrollable frame for the table
        canvas = tk.Canvas(wells_frame)
        scrollbar = ttk.Scrollbar(wells_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Create table headers
        headers = ["Well", "Top Boundary", "Top Depth", "Bottom Boundary", "Bottom Depth", "Status"]
        for col, header in enumerate(headers):
            label = ttk.Label(scrollable_frame, text=header, font=("", 10, "bold"))
            label.grid(row=0, column=col, padx=5, pady=5, sticky="w")

        # Dictionary to store selections for each well
        selections = {}
        result = {"boundaries": {}}

        # Function to enable/disable common boundary controls
        def toggle_common_controls():
            state = "normal" if use_common_var.get() else "disabled"
            common_top_combo.config(state=state)
            common_bottom_combo.config(state=state)
            apply_common_btn.config(state=state)
            
            if not use_common_var.get():
                common_status_var.set("")

        # Function to apply common boundaries to all wells
        def apply_common_boundaries():
            if not use_common_var.get():
                return
                
            top_selection = common_top_var.get()
            bottom_selection = common_bottom_var.get()
            
            if not top_selection or not bottom_selection:
                messagebox.showwarning("Incomplete Selection", "Please select both top and bottom common boundaries.")
                return
            
            # Extract surface names from display text
            top_surface = None
            bottom_surface = None
            for surface, display in common_surface_options:
                if display == top_selection:
                    top_surface = surface
                if display == bottom_selection:
                    bottom_surface = surface
            
            if not top_surface or not bottom_surface:
                messagebox.showerror("Error", "Could not identify selected surfaces.")
                return
            
            # Apply to all wells
            applied_count = 0
            skipped_wells = []
            
            for well_name in selections:
                if well_name in well_data_dict:
                    well_df = well_data_dict[well_name]
                    
                    # Check if both surfaces exist in this well
                    well_surfaces = well_df['Surface'].unique()
                    if top_surface in well_surfaces and bottom_surface in well_surfaces:
                        # Apply the boundaries
                        selections[well_name]["top_surface"].set(top_surface)
                        selections[well_name]["bottom_surface"].set(bottom_surface)
                        
                        # Update depths
                        top_data = well_df[well_df['Surface'] == top_surface]
                        bottom_data = well_df[well_df['Surface'] == bottom_surface]
                        
                        if not top_data.empty and not bottom_data.empty:
                            top_depth = top_data['MD'].iloc[0]
                            bottom_depth = bottom_data['MD'].iloc[0]
                            
                            selections[well_name]["top_depth_var"].set(f"{top_depth:.2f}")
                            selections[well_name]["bottom_depth_var"].set(f"{bottom_depth:.2f}")
                            selections[well_name]["top_depth"] = top_depth
                            selections[well_name]["bottom_depth"] = bottom_depth
                            selections[well_name]["status_var"].set("✓ Common")
                            applied_count += 1
                        else:
                            selections[well_name]["status_var"].set("⚠ Data Error")
                            skipped_wells.append(well_name)
                    else:
                        selections[well_name]["status_var"].set("⚠ Missing Surface")
                        skipped_wells.append(well_name)
            
            # Update status
            if skipped_wells:
                common_status_var.set(f"Applied to {applied_count} wells. Skipped: {', '.join(skipped_wells)}")
            else:
                common_status_var.set(f"Successfully applied to all {applied_count} wells.")

        # Bind events
        use_common_var.trace_add("write", lambda *args: toggle_common_controls())
        apply_common_btn.config(command=apply_common_boundaries)

        # Create rows for each well
        row = 1
        for well_name in available_wells:
            if well_name in well_data_dict:
                well_df = well_data_dict[well_name]
                surfaces = well_df['Surface'].unique().tolist()

                if len(surfaces) < 2:
                    # Skip wells with fewer than 2 surfaces (need at least top and bottom)
                    continue

                # Create a dictionary to store selections for this well
                selections[well_name] = {
                    "top_surface": tk.StringVar(),
                    "bottom_surface": tk.StringVar(),
                    "top_depth_var": tk.StringVar(),
                    "bottom_depth_var": tk.StringVar(),
                    "status_var": tk.StringVar(),
                    "top_depth": None,
                    "bottom_depth": None
                }

                # Well name label
                well_label = ttk.Label(scrollable_frame, text=well_name)
                well_label.grid(row=row, column=0, padx=5, pady=2, sticky="w")

                # Top boundary dropdown
                top_combo = ttk.Combobox(scrollable_frame, textvariable=selections[well_name]["top_surface"],
                                       values=surfaces, state="readonly", width=15)
                top_combo.grid(row=row, column=1, padx=5, pady=2)
                if surfaces:
                    top_combo.set(surfaces[0])  # Default to first surface

                # Top depth display
                top_depth_label = ttk.Label(scrollable_frame, textvariable=selections[well_name]["top_depth_var"])
                top_depth_label.grid(row=row, column=2, padx=5, pady=2)

                # Bottom boundary dropdown
                bottom_combo = ttk.Combobox(scrollable_frame, textvariable=selections[well_name]["bottom_surface"],
                                          values=surfaces, state="readonly", width=15)
                bottom_combo.grid(row=row, column=3, padx=5, pady=2)
                if len(surfaces) > 1:
                    bottom_combo.set(surfaces[-1])  # Default to last surface

                # Bottom depth display
                bottom_depth_label = ttk.Label(scrollable_frame, textvariable=selections[well_name]["bottom_depth_var"])
                bottom_depth_label.grid(row=row, column=4, padx=5, pady=2)

                # Status display
                status_label = ttk.Label(scrollable_frame, textvariable=selections[well_name]["status_var"])
                status_label.grid(row=row, column=5, padx=5, pady=2)

                # Function to update depth displays when surface selection changes
                def update_depths(well=well_name, well_data=well_df):
                    def _update():
                        top_surface = selections[well]["top_surface"].get()
                        bottom_surface = selections[well]["bottom_surface"].get()

                        # Get depths for selected surfaces
                        if top_surface:
                            top_data = well_data[well_data['Surface'] == top_surface]
                            if not top_data.empty:
                                top_depth = top_data['MD'].iloc[0]
                                selections[well]["top_depth_var"].set(f"{top_depth:.2f}")
                                selections[well]["top_depth"] = top_depth

                        if bottom_surface:
                            bottom_data = well_data[well_data['Surface'] == bottom_surface]
                            if not bottom_data.empty:
                                bottom_depth = bottom_data['MD'].iloc[0]
                                selections[well]["bottom_depth_var"].set(f"{bottom_depth:.2f}")
                                selections[well]["bottom_depth"] = bottom_depth
                        
                        # Update status to indicate manual selection
                        if not use_common_var.get():
                            selections[well]["status_var"].set("Manual")
                    return _update

                # Bind the update function to combobox changes
                update_func = update_depths()
                top_combo.bind("<<ComboboxSelected>>", lambda e, func=update_func: func())
                bottom_combo.bind("<<ComboboxSelected>>", lambda e, func=update_func: func())

                # Initialize depth displays
                update_func()

                row += 1

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)

        def on_ok():
            # Show a progress indicator
            status_label = ttk.Label(main_frame, text="Processing selections...", font=("", 10, "italic"))
            status_label.pack(before=button_frame, pady=5)
            dialog.update_idletasks()  # Force UI update to show the status label

            # Validate selections
            invalid_wells = []
            for well_name, selection in selections.items():
                top_depth = selection["top_depth"]
                bottom_depth = selection["bottom_depth"]

                if top_depth is None or bottom_depth is None:
                    invalid_wells.append(well_name)
                else:
                    # Store the boundaries
                    result["boundaries"][well_name] = (top_depth, bottom_depth)

            if invalid_wells:
                messagebox.showwarning(
                    "Incomplete Selections",
                    f"The following wells have incomplete boundary selections and will be skipped:\n{', '.join(invalid_wells)}"
                )

            # Show summary of applied boundaries
            if result["boundaries"]:
                summary_msg = f"Successfully processed {len(result['boundaries'])} wells:\n\n"
                for well, (top, bottom) in result["boundaries"].items():
                    status = selections[well]["status_var"].get()
                    summary_msg += f"• {well}: {top:.2f} - {bottom:.2f} ({status})\n"
                
                if len(summary_msg) > 500:  # Truncate if too long
                    summary_msg = summary_msg[:500] + "...\n\n[Additional wells not shown]"
                
                messagebox.showinfo("Boundary Selection Complete", summary_msg)

            dialog.destroy()

        def on_cancel():
            result["boundaries"] = None
            dialog.destroy()

        ok_button = ttk.Button(button_frame, text="OK", command=on_ok)
        ok_button.pack(side=tk.LEFT, padx=5)

        cancel_button = ttk.Button(button_frame, text="Cancel", command=on_cancel)
        cancel_button.pack(side=tk.LEFT, padx=5)

        # Initialize common boundary controls
        toggle_common_controls()

        # Wait for dialog to close
        dialog.wait_window()

        return result["boundaries"]

    def get_depth_ranges(self, las_files, log_keywords_for_finding_cols, preloaded_excel_df=None):
        """
        Create a GUI to display well names and allow users to input top and bottom depths for analysis.
        Provides two methods:
        1. Manual input: Directly enter depth values
        2. Excel file import: Select depths from geological markers in an Excel file

        Args:
            las_files: List of LAS file objects
            log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
            preloaded_excel_df: Optional pre-loaded Excel DataFrame with depth ranges

        If no input is provided, use the min and max non-NaN values from the DT log, if available.
        """
        # Calculate default depth values for each well
        default_depth_values_for_fallback = {}

        for las in las_files:
            well_name = las.well.WELL.value

            current_well_cols_for_depth = find_default_columns(las, log_keywords_for_finding_cols)
            depth_mnemonic = current_well_cols_for_depth.get('DEPTH')
            dt_mnemonic = current_well_cols_for_depth.get('DT')

            default_top_depth_val = 0.0  # Fallback default
            default_bottom_depth_val = 0.0 # Fallback default

            if depth_mnemonic and depth_mnemonic in las.curves:
                depth_data_for_range = np.array(las[depth_mnemonic].data)

                if dt_mnemonic and dt_mnemonic in las.curves:
                    dt_data_for_range = np.array(las[dt_mnemonic].data)
                    # Consider only depths where DT is valid
                    valid_dt_mask = np.isfinite(dt_data_for_range) & np.isfinite(depth_data_for_range)
                    valid_depths_for_dt_range = depth_data_for_range[valid_dt_mask]

                    if valid_depths_for_dt_range.size > 0:
                        default_top_depth_val = np.min(valid_depths_for_dt_range)
                        default_bottom_depth_val = np.max(valid_depths_for_dt_range)
                    elif np.any(np.isfinite(depth_data_for_range)): # Fallback to full depth range if DT is all NaN
                        default_top_depth_val = np.nanmin(depth_data_for_range)
                        default_bottom_depth_val = np.nanmax(depth_data_for_range)
                        print(f"Info: DT log '{dt_mnemonic}' for well {well_name} has no valid data points within depth range. Using full depth range for defaults.")
                    else: # Depth log itself is all NaN or empty
                        print(f"Warning: Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN. Using 0,0 for default depth range.")
                elif np.any(np.isfinite(depth_data_for_range)): # DT log not found, use full depth range
                    default_top_depth_val = np.nanmin(depth_data_for_range)
                    default_bottom_depth_val = np.nanmax(depth_data_for_range)
                    print(f"Info: DT log not found for well {well_name} (searched for '{dt_mnemonic}'). Using full depth range for defaults.")
                else: # Depth log is all NaN or empty, and DT log not found
                     print(f"Warning: Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN, and DT log not found. Using 0,0 for default depth range.")
            else:
                print(f"Error: DEPTH log not found for well {well_name} (searched for '{depth_mnemonic}'). Cannot set default depth range. Using 0,0.")

            default_depth_values_for_fallback[well_name] = (default_top_depth_val, default_bottom_depth_val)

        # Create main dialog for method selection
        root = tk.Tk()
        root.title("Set Depth Ranges for Wells")
        root.geometry("600x500")

        # Create a frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Method selection
        method_frame = ttk.LabelFrame(main_frame, text="Select Method for Defining Boundaries", padding="10")
        method_frame.pack(fill=tk.X, padx=5, pady=5)

        # Set default method - use "excel" if preloaded Excel data is available
        default_method = "excel" if preloaded_excel_df is not None else "manual"
        method_var = tk.StringVar(value=default_method)

        # Variable to store the submit button reference
        submit_btn_ref = {"btn": None}

        # Define a command to call when radio buttons are clicked
        def on_radio_click():
            method = method_var.get()

            if method == "manual":
                create_manual_ui()
                # Re-enable the Submit button for manual input
                if submit_btn_ref["btn"] is not None:
                    submit_btn_ref["btn"].config(state=tk.NORMAL)
            else:  # excel
                create_excel_ui()
                # Enable Submit button only if Excel data is available
                if submit_btn_ref["btn"] is not None:
                    if excel_data["df"] is not None:
                        submit_btn_ref["btn"].config(state=tk.NORMAL)
                    else:
                        submit_btn_ref["btn"].config(state=tk.DISABLED)

        ttk.Radiobutton(
            method_frame,
            text="Manual Input",
            variable=method_var,
            value="manual",
            command=on_radio_click
        ).pack(anchor=tk.W, pady=2)

        ttk.Radiobutton(
            method_frame,
            text="Import from Excel File",
            variable=method_var,
            value="excel",
            command=on_radio_click
        ).pack(anchor=tk.W, pady=2)

        # Content frame for the selected method (don't expand fully to leave space for buttons)
        content_frame = ttk.Frame(main_frame, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))

        # Button frame at the bottom (always visible)
        button_frame = ttk.Frame(main_frame, padding="10")
        button_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Variables to store entries for manual method
        manual_entries = []

        # Variable to store Excel data
        excel_data = {"df": preloaded_excel_df}

        # Function to create manual input UI
        def create_manual_ui():
            # Clear the content frame
            for widget in content_frame.winfo_children():
                widget.destroy()

            # Add instructions for manual input
            instructions_frame = ttk.LabelFrame(content_frame, text="Manual Depth Input Instructions", padding="10")
            instructions_frame.pack(fill=tk.X, pady=(0, 10))

            instructions_text = (
                "Enter depth values directly in the fields below:\n"
                "• Top Depth: Starting depth for analysis (typically shallower/smaller value)\n"
                "• Bottom Depth: Ending depth for analysis (typically deeper/larger value)\n"
                "• Values should be in the same units as your LAS files (usually meters or feet)\n"
                "• Top depth must be less than bottom depth for each well"
            )
            ttk.Label(instructions_frame, text=instructions_text, justify=tk.LEFT, wraplength=550).pack(anchor=tk.W)

            # Create scrollable frame for many wells
            canvas = tk.Canvas(content_frame)
            scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Create and set column headers with units
            ttk.Label(scrollable_frame, text="Well Name", font=("", 9, "bold")).grid(row=0, column=0, padx=5, pady=5)
            ttk.Label(scrollable_frame, text="Top Depth (m/ft)", font=("", 9, "bold")).grid(row=0, column=1, padx=5, pady=5)
            ttk.Label(scrollable_frame, text="Bottom Depth (m/ft)", font=("", 9, "bold")).grid(row=0, column=2, padx=5, pady=5)
            ttk.Label(scrollable_frame, text="Depth Range", font=("", 9, "bold")).grid(row=0, column=3, padx=5, pady=5)

            # Clear previous entries
            manual_entries.clear()

            # Create entries for each well
            for i, las in enumerate(las_files, start=1):
                well_name = las.well.WELL.value
                default_top, default_bottom = default_depth_values_for_fallback[well_name]

                ttk.Label(scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2, sticky=tk.W)

                top_entry = ttk.Entry(scrollable_frame, width=12)
                top_entry.insert(0, f"{default_top:.2f}")
                top_entry.grid(row=i, column=1, padx=5, pady=2)

                bottom_entry = ttk.Entry(scrollable_frame, width=12)
                bottom_entry.insert(0, f"{default_bottom:.2f}")
                bottom_entry.grid(row=i, column=2, padx=5, pady=2)

                # Add depth range display
                range_label = ttk.Label(scrollable_frame, text=f"{default_bottom - default_top:.2f}",
                                      foreground="gray", font=("", 8))
                range_label.grid(row=i, column=3, padx=5, pady=2)

                manual_entries.append((well_name, top_entry, bottom_entry, range_label))

        # Function to create Excel input UI
        def create_excel_ui():
            # Clear the content frame
            for widget in content_frame.winfo_children():
                widget.destroy()

            # Add instructions for Excel import
            instructions_frame = ttk.LabelFrame(content_frame, text="Excel Import Instructions", padding="10")
            instructions_frame.pack(fill=tk.X, pady=(0, 10))

            instructions_text = (
                "Import depth boundaries from geological markers in an Excel file:\n"
                "• Excel file must contain columns: 'Well', 'Surface', 'MD' (measured depth)\n"
                "• 'Well' column should match your LAS file well names\n"
                "• 'Surface' column contains geological marker names (e.g., 'Top_Formation', 'Base_Formation')\n"
                "• 'MD' column contains measured depths for each marker\n"
                "• After loading, you'll select top and bottom markers for each well"
            )
            ttk.Label(instructions_frame, text=instructions_text, justify=tk.LEFT, wraplength=550).pack(anchor=tk.W)

            # Create Excel file selection frame
            excel_frame = ttk.LabelFrame(content_frame, text="Excel File Selection", padding="10")
            excel_frame.pack(fill=tk.X, pady=5)

            # File path display
            file_path_var = tk.StringVar()
            if excel_data["df"] is not None:
                file_path_var.set("✓ Pre-loaded Excel data available")
                file_label_color = "green"
            else:
                file_path_var.set("⚠ No file selected")
                file_label_color = "red"

            ttk.Label(excel_frame, text="Status:").pack(anchor=tk.W)
            file_label = ttk.Label(excel_frame, textvariable=file_path_var, foreground=file_label_color)
            file_label.pack(anchor=tk.W, pady=(0, 5))

            def browse_excel_file():
                file_path = filedialog.askopenfilename(
                    title="Select Excel File with Depth Boundaries",
                    filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
                )
                if file_path:
                    try:
                        df = load_boundaries_from_excel(file_path)
                        if df is not None:
                            excel_data["df"] = df
                            file_path_var.set(f"✓ Loaded: {file_path}")
                            file_label.config(foreground="green")
                            # Enable the Submit button
                            if submit_btn_ref["btn"] is not None:
                                submit_btn_ref["btn"].config(state=tk.NORMAL)
                        else:
                            file_path_var.set("⚠ Failed to load file")
                            file_label.config(foreground="red")
                            messagebox.showerror("Error",
                                "Failed to load Excel file. Please check the file format.\n\n"
                                "Required columns: 'Well', 'Surface', 'MD'")
                    except Exception as e:
                        file_path_var.set("⚠ Error loading file")
                        file_label.config(foreground="red")
                        messagebox.showerror("Error", f"Error loading Excel file: {str(e)}")

            # Show browse button
            if excel_data["df"] is None:
                browse_button = ttk.Button(excel_frame, text="Browse Excel File", command=browse_excel_file)
                browse_button.pack(anchor=tk.W, pady=(5, 0))

                # Add fallback option
                fallback_frame = ttk.Frame(excel_frame)
                fallback_frame.pack(fill=tk.X, pady=(10, 0))

                ttk.Label(fallback_frame, text="Or:", font=("", 9, "italic")).pack(anchor=tk.W)
                fallback_button = ttk.Button(fallback_frame, text="Switch to Manual Input",
                                           command=lambda: [method_var.set("manual"), on_radio_click()])
                fallback_button.pack(anchor=tk.W, pady=(2, 0))
            else:
                # Enable the Submit button for pre-loaded data
                if submit_btn_ref["btn"] is not None:
                    submit_btn_ref["btn"].config(state=tk.NORMAL)

                # Show option to load different file
                change_button = ttk.Button(excel_frame, text="Load Different Excel File", command=browse_excel_file)
                change_button.pack(anchor=tk.W, pady=(5, 0))

        # Initialize UI based on default method
        if default_method == "manual":
            create_manual_ui()
        else:
            create_excel_ui()

        # Force radio button to trigger UI update to ensure synchronization
        # This ensures the UI matches the radio button state
        root.after(100, on_radio_click)  # Delay to ensure UI is fully initialized

        # Output variable
        depth_ranges_output = {}

        # Validation helper function
        def validate_manual_entries():
            """
            Validate all manual input entries with comprehensive checks.
            Returns (is_valid, error_message, problematic_well)
            """
            validation_errors = []

            for entry_data in manual_entries:
                if len(entry_data) == 4:  # New format with range label
                    well_name, top_entry, bottom_entry, range_label = entry_data
                else:  # Old format compatibility
                    well_name, top_entry, bottom_entry = entry_data[:3]

                top_str = top_entry.get().strip()
                bottom_str = bottom_entry.get().strip()

                # Check for empty fields
                if not top_str or not bottom_str:
                    validation_errors.append(f"Well '{well_name}': Both top and bottom depths are required")
                    continue

                try:
                    top_depth = float(top_str)
                    bottom_depth = float(bottom_str)
                except ValueError:
                    validation_errors.append(f"Well '{well_name}': Depth values must be numeric")
                    continue

                # Check that top < bottom
                if top_depth >= bottom_depth:
                    validation_errors.append(f"Well '{well_name}': Top depth ({top_depth:.2f}) must be less than bottom depth ({bottom_depth:.2f})")
                    continue

                # Check for reasonable values (non-negative, not too extreme)
                if top_depth < 0 or bottom_depth < 0:
                    validation_errors.append(f"Well '{well_name}': Depth values should be non-negative")
                    continue

                if bottom_depth - top_depth < 0.1:
                    validation_errors.append(f"Well '{well_name}': Depth range too small (minimum 0.1 units)")
                    continue

                if bottom_depth - top_depth > 50000:  # Reasonable maximum range
                    validation_errors.append(f"Well '{well_name}': Depth range seems too large ({bottom_depth - top_depth:.2f} units)")
                    continue

                # Check against well data ranges if available
                well_default_top, well_default_bottom = default_depth_values_for_fallback.get(well_name, (0, 0))
                if well_default_top != 0 and well_default_bottom != 0:  # Valid defaults available
                    well_range = well_default_bottom - well_default_top
                    if well_range > 0:
                        # Allow some flexibility but warn if way outside well data range
                        buffer = well_range * 0.5  # 50% buffer
                        if (top_depth < well_default_top - buffer or
                            bottom_depth > well_default_bottom + buffer):
                            validation_errors.append(
                                f"Well '{well_name}': Specified range ({top_depth:.2f}-{bottom_depth:.2f}) "
                                f"extends significantly beyond well data range "
                                f"({well_default_top:.2f}-{well_default_bottom:.2f})"
                            )

            if validation_errors:
                error_message = "Validation errors found:\n\n" + "\n".join(validation_errors)
                return False, error_message, None

            return True, "", None

        # Submit function
        def submit():
            method = method_var.get()

            if method == "manual":
                # Check if manual entries exist - if not, there's a UI synchronization issue
                if not manual_entries:
                    # Attempt to fix the synchronization issue by recreating the manual UI
                    try:
                        create_manual_ui()

                        if not manual_entries:
                            # Still empty after recreation - this is a serious issue
                            messagebox.showerror(
                                "UI Error",
                                "Failed to initialize manual input interface.\n\n"
                                "This may be due to missing well data. Please check your LAS files."
                            )
                            return
                    except Exception as e:
                        messagebox.showerror(
                            "UI Error",
                            f"Failed to initialize manual input interface: {str(e)}\n\n"
                            "Please try restarting the depth range selection."
                        )
                        return

                # Validate manual entries
                is_valid, error_message, problematic_well = validate_manual_entries()
                if not is_valid:
                    messagebox.showerror("Input Validation Error", error_message)
                    return

                # Process manual entries (validation passed)
                for entry_data in manual_entries:
                    if len(entry_data) == 4:  # New format with range label
                        well_name, top_entry, bottom_entry, range_label = entry_data
                    else:  # Old format compatibility
                        well_name, top_entry, bottom_entry = entry_data[:3]

                    top_depth = float(top_entry.get().strip())
                    bottom_depth = float(bottom_entry.get().strip())
                    depth_ranges_output[well_name] = (top_depth, bottom_depth)

                root.quit()

            else:  # excel
                if excel_data["df"] is None:
                    # Enhanced fallback logic
                    result = messagebox.askyesno(
                        "No Excel Data",
                        "No Excel file has been loaded.\n\n"
                        "Would you like to switch to Manual Input mode instead?\n"
                        "(Click 'No' to stay in Excel mode and load a file)"
                    )
                    if result:  # User wants to switch to manual
                        method_var.set("manual")
                        on_radio_click()
                        return
                    else:  # User wants to stay in Excel mode
                        return

                df_excel = excel_data["df"]

                # Validate Excel data before proceeding
                las_well_names = [las.well.WELL.value for las in las_files]
                wells_in_excel = df_excel['Well'].unique() if 'Well' in df_excel.columns else []
                matching_wells = [well for well in las_well_names if well in wells_in_excel]

                if not matching_wells:
                    result = messagebox.askyesno(
                        "No Matching Wells",
                        f"No wells from your LAS files were found in the Excel data.\n\n"
                        f"LAS wells: {', '.join(las_well_names)}\n"
                        f"Excel wells: {', '.join(wells_in_excel)}\n\n"
                        f"Would you like to switch to Manual Input mode instead?"
                    )
                    if result:  # User wants to switch to manual
                        method_var.set("manual")
                        on_radio_click()
                        return
                    else:  # User wants to stay and try different file
                        return

                # Update status to indicate batch selection is starting
                status_var = tk.StringVar(value="Starting batch selection dialog...")
                status_label = ttk.Label(button_frame, textvariable=status_var, font=("", 10, "italic"))
                status_label.pack(before=submit_btn_ref["btn"], pady=2)
                root.update_idletasks()  # Force UI update

                # Show a dialog to select boundaries for all wells at once
                print("Opening batch selection dialog for all wells...")
                try:
                    all_boundaries = self._select_boundaries_for_all_wells(df_excel, las_well_names)
                except Exception as e:
                    status_var.set("Error in boundary selection.")
                    messagebox.showerror("Error", f"Error during boundary selection: {str(e)}")
                    return

                # Update status based on the result
                if all_boundaries:
                    status_var.set("Processing selected boundaries...")
                    root.update_idletasks()  # Force UI update

                    # Apply the selected boundaries
                    for well_name, boundaries in all_boundaries.items():
                        depth_ranges_output[well_name] = boundaries
                        print(f"Boundaries set for {well_name} from Excel batch selection: {boundaries}")

                    status_var.set(f"Successfully processed {len(all_boundaries)} wells from Excel data.")
                    root.update_idletasks()  # Force UI update

                    # Close the main dialog after a short delay
                    root.after(1000, root.quit)  # Close after 1 second
                else:
                    # User cancelled the batch selection dialog
                    status_var.set("Batch selection was cancelled.")
                    root.update_idletasks()  # Force UI update

                    # Offer fallback to manual input
                    result = messagebox.askyesno(
                        "Selection Cancelled",
                        "Boundary selection was cancelled.\n\n"
                        "Would you like to switch to Manual Input mode to enter depths directly?"
                    )
                    if result:  # User wants to switch to manual
                        method_var.set("manual")
                        on_radio_click()
                        return

        # Create Submit button in the dedicated button frame
        submit_button = ttk.Button(button_frame, text="Submit", command=submit)
        submit_button.pack(pady=5)
        submit_btn_ref["btn"] = submit_button

        # Set initial button state
        if default_method == "excel" and excel_data["df"] is None:
            submit_button.config(state=tk.DISABLED)
        else:
            submit_button.config(state=tk.NORMAL)

        # Start the main loop
        root.mainloop()
        root.destroy()

        return depth_ranges_output

    def get_module_info(self):
        """
        Get information about this dialog systems module.

        Returns:
            dict: Module information including version, functions, etc.
        """
        return {
            'module_name': 'Dialog Systems',
            'version': '1.0.0',
            'functions': [
                'get_analysis_type_and_parameters',
                'show_next_action_dialog',
                'get_target_log',
                'get_depth_ranges',
                'select_alternative_mnemonic'
            ],
            'status': 'Phase 2C - Complete',
            'extracted_functions': 5,
            'total_functions': 5,
            'completion_percentage': 100
        }


# Global instance for backward compatibility
dialog_systems = DialogSystems()


# Legacy wrapper functions for backward compatibility
def get_analysis_type_and_parameters():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_analysis_type_and_parameters()


def show_next_action_dialog():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.show_next_action_dialog()


def select_alternative_mnemonic(las, missing_target_log, well_name):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.select_alternative_mnemonic(las, missing_target_log, well_name)


def get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used=False):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords, calculator_used)


def get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df=None):
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df)


# Create a global instance for backward compatibility
dialog_systems = DialogSystems()
